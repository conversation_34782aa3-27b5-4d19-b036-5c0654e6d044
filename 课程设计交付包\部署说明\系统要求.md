# 🚗 车辆管理系统 - 系统要求

## 📋 **硬件要求**

### 最低配置
- **CPU**: Intel Core i3 或 AMD 同等级别
- **内存**: 4GB RAM
- **硬盘**: 2GB 可用空间
- **网络**: 支持TCP/IP协议

### 推荐配置
- **CPU**: Intel Core i5 或更高
- **内存**: 8GB RAM 或更高
- **硬盘**: 5GB 可用空间
- **网络**: 千兆以太网

## 💻 **软件要求**

### 操作系统
- **Windows**: Windows 10/11 (推荐)
- **Windows Server**: 2016/2019/2022
- **Linux**: Ubuntu 18.04+ / CentOS 7+ (可选)

### 必需软件

#### 1. Java 运行环境
- **版本**: JDK 8 或更高版本
- **下载地址**: https://www.oracle.com/java/technologies/downloads/
- **环境变量**: 需要配置 JAVA_HOME 和 PATH

#### 2. SQL Server 数据库
- **版本**: SQL Server 2017/2019/2022
- **版本类型**: Express/Standard/Enterprise 均可
- **下载地址**: https://www.microsoft.com/sql-server/sql-server-downloads
- **配置要求**: 
  - 启用TCP/IP协议
  - 支持混合身份验证模式

#### 3. Web服务器 (二选一)

##### 选项A: Apache Tomcat (推荐)
- **版本**: Tomcat 8.5+ 或 Tomcat 9.x
- **下载地址**: https://tomcat.apache.org/download-90.cgi
- **配置**: 默认端口8080

##### 选项B: 其他Java Web服务器
- **Jetty**: 9.x 版本
- **WildFly**: 20.x 版本
- **WebLogic**: 12c 版本

## 🔧 **开发工具 (可选)**

如需修改源代码，建议安装：

### IDE
- **IntelliJ IDEA**: Community/Ultimate 版本
- **Eclipse**: Java EE 版本
- **Visual Studio Code**: 配合Java扩展

### 构建工具
- **Maven**: 3.6+ 版本
- **下载地址**: https://maven.apache.org/download.cgi

### 数据库管理工具
- **SQL Server Management Studio (SSMS)**
- **Azure Data Studio**
- **DBeaver** (跨平台)

## 🌐 **网络要求**

### 端口配置
- **Web服务**: 8080 (Tomcat默认)
- **数据库**: 1433 (SQL Server默认)
- **防火墙**: 需要开放上述端口

### 安全要求
- 确保防火墙允许相关端口通信
- 数据库用户权限配置正确
- Web服务器安全配置

## 📱 **客户端要求**

### 浏览器支持
- **Chrome**: 80+ 版本 (推荐)
- **Firefox**: 75+ 版本
- **Edge**: 80+ 版本
- **Safari**: 13+ 版本

### 分辨率
- **最低**: 1024x768
- **推荐**: 1920x1080 或更高

## ⚡ **性能指标**

### 响应时间
- **页面加载**: < 3秒
- **数据查询**: < 2秒
- **数据提交**: < 1秒

### 并发用户
- **最大并发**: 50用户 (推荐配置下)
- **数据库连接**: 20个并发连接

## 🔍 **兼容性说明**

### 已测试环境
- ✅ Windows 10 + JDK 8 + SQL Server 2022 + Tomcat 9
- ✅ Windows 11 + JDK 11 + SQL Server 2019 + Tomcat 8.5
- ✅ Windows Server 2019 + JDK 8 + SQL Server 2017

### 注意事项
- 确保所有软件版本兼容
- 建议在测试环境先验证
- 生产环境部署前进行完整测试
