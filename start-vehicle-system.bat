@echo off
title Vehicle Management System

echo ========================================
echo Vehicle Management System Launcher
echo ========================================
echo.

REM Disable conda completely
set CONDA_DEFAULT_ENV=
set CONDA_EXE=
set CONDA_PREFIX=
set CONDA_PROMPT_MODIFIER=
set CONDA_PYTHON_EXE=
set CONDA_ROOT=
set CONDA_SHLVL=
set PYTHONPATH=
set PYTHONIOENCODING=

REM Set minimal PATH to avoid conda interference
set "PATH=C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem"

REM Set Java and Maven full paths
set "JAVA_HOME=D:\javaJDK8"
set "MAVEN_HOME=D:\XunLai\apache-maven-3.9.10-bin\apache-maven-3.9.10"

echo Checking Java...
if exist "%JAVA_HOME%\bin\java.exe" (
    echo Java found: %JAVA_HOME%\bin\java.exe
    "%JAVA_HOME%\bin\java.exe" -version
    if errorlevel 1 (
        echo ERROR: Java test failed!
        pause
        exit /b 1
    )
) else (
    echo ERROR: Java not found at: %JAVA_HOME%\bin\java.exe
    pause
    exit /b 1
)

echo.
echo Checking Maven...
if exist "%MAVEN_HOME%\bin\mvn.cmd" (
    echo Maven found: %MAVEN_HOME%\bin\mvn.cmd
    "%MAVEN_HOME%\bin\mvn.cmd" -version
    if errorlevel 1 (
        echo ERROR: Maven test failed!
        pause
        exit /b 1
    )
) else (
    echo ERROR: Maven not found at: %MAVEN_HOME%\bin\mvn.cmd
    pause
    exit /b 1
)

echo.
echo Changing to project directory...
cd /d "D:\projects\vehicle-system"
if errorlevel 1 (
    echo ERROR: Cannot find project directory!
    pause
    exit /b 1
)

echo Current directory: %CD%

echo.
echo Checking pom.xml...
if exist "pom.xml" (
    echo pom.xml file exists
) else (
    echo ERROR: pom.xml file not found!
    pause
    exit /b 1
)

echo.
echo ========================================
echo Starting project...
echo ========================================
echo Project will be available at:
echo   http://localhost:8080/vehicle
echo.
echo Press Ctrl+C to stop the server
echo ========================================
echo.

REM Start Maven using full path
"%MAVEN_HOME%\bin\mvn.cmd" clean compile tomcat7:run

echo.
echo Project stopped.
pause
