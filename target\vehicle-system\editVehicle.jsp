<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.sql.*" %>
<%@ page import="com.vehicle.util.DBUtil" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>编辑车辆 - 车辆管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .form-container {
            padding: 40px;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .form-actions {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }
        
        .required {
            color: #dc3545;
        }
        
        .form-help {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .vehicle-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        
        .vehicle-info h3 {
            color: #495057;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✏️ 编辑车辆信息</h1>
            <p>修改车辆详细信息</p>
        </div>
        
        <div class="form-container">
            <%
                String message = "";
                String messageType = "";
                String vehicleId = request.getParameter("id");
                
                // 车辆信息变量
                String licensePlate = "";
                String brand = "";
                String model = "";
                String purchaseDate = "";
                String mileage = "";
                String status = "";
                
                if (vehicleId == null || vehicleId.trim().isEmpty()) {
                    response.sendRedirect("vehicles.jsp");
                    return;
                }
                
                // 处理表单提交
                if ("POST".equals(request.getMethod())) {
                    licensePlate = request.getParameter("licensePlate");
                    brand = request.getParameter("brand");
                    model = request.getParameter("model");
                    purchaseDate = request.getParameter("purchaseDate");
                    String mileageStr = request.getParameter("mileage");
                    status = request.getParameter("status");
                    
                    // 验证必填字段
                    if (licensePlate != null && !licensePlate.trim().isEmpty() &&
                        brand != null && !brand.trim().isEmpty() &&
                        model != null && !model.trim().isEmpty()) {
                        
                        try {
                            int mileageInt = 0;
                            if (mileageStr != null && !mileageStr.trim().isEmpty()) {
                                mileageInt = Integer.parseInt(mileageStr);
                            }
                            
                            Connection conn = DBUtil.getConnection();
                            String sql = "UPDATE vehicle SET license_plate=?, brand=?, model=?, purchase_date=?, mileage=?, status=? WHERE id=?";
                            PreparedStatement pstmt = conn.prepareStatement(sql);
                            
                            pstmt.setString(1, licensePlate.trim());
                            pstmt.setString(2, brand.trim());
                            pstmt.setString(3, model.trim());
                            
                            if (purchaseDate != null && !purchaseDate.trim().isEmpty()) {
                                pstmt.setDate(4, Date.valueOf(purchaseDate));
                            } else {
                                pstmt.setNull(4, Types.DATE);
                            }
                            
                            pstmt.setInt(5, mileageInt);
                            pstmt.setString(6, status != null ? status : "available");
                            pstmt.setInt(7, Integer.parseInt(vehicleId));
                            
                            int result = pstmt.executeUpdate();
                            
                            if (result > 0) {
                                message = "✅ 车辆信息更新成功！";
                                messageType = "success";
                            } else {
                                message = "❌ 更新失败，请重试。";
                                messageType = "error";
                            }
                            
                            pstmt.close();
                            conn.close();
                            
                        } catch (SQLException e) {
                            if (e.getMessage().contains("PRIMARY KEY") || e.getMessage().contains("UNIQUE")) {
                                message = "❌ 车牌号已存在，请使用其他车牌号。";
                            } else {
                                message = "❌ 数据库错误：" + e.getMessage();
                            }
                            messageType = "error";
                        } catch (NumberFormatException e) {
                            message = "❌ 里程数必须是有效的数字。";
                            messageType = "error";
                        } catch (IllegalArgumentException e) {
                            message = "❌ 购买日期格式不正确，请使用 YYYY-MM-DD 格式。";
                            messageType = "error";
                        }
                    } else {
                        message = "❌ 请填写所有必填字段（车牌号、品牌、型号）。";
                        messageType = "error";
                    }
                }
                
                // 获取车辆信息
                try {
                    Connection conn = DBUtil.getConnection();
                    String sql = "SELECT * FROM vehicle WHERE id = ?";
                    PreparedStatement pstmt = conn.prepareStatement(sql);
                    pstmt.setInt(1, Integer.parseInt(vehicleId));
                    ResultSet rs = pstmt.executeQuery();
                    
                    if (rs.next()) {
                        // 如果不是POST请求，使用数据库中的值
                        if (!"POST".equals(request.getMethod())) {
                            licensePlate = rs.getString("license_plate");
                            brand = rs.getString("brand");
                            model = rs.getString("model");
                            Date dbPurchaseDate = rs.getDate("purchase_date");
                            purchaseDate = dbPurchaseDate != null ? dbPurchaseDate.toString() : "";
                            mileage = String.valueOf(rs.getInt("mileage"));
                            status = rs.getString("status");
                        }
                    } else {
                        message = "❌ 未找到指定的车辆信息。";
                        messageType = "error";
                    }
                    
                    rs.close();
                    pstmt.close();
                    conn.close();
                    
                } catch (SQLException e) {
                    message = "❌ 数据库连接错误：" + e.getMessage();
                    messageType = "error";
                } catch (NumberFormatException e) {
                    message = "❌ 无效的车辆ID。";
                    messageType = "error";
                }
            %>
            
            <% if (!message.isEmpty()) { %>
                <div class="<%= messageType.equals("success") ? "success-message" : "error-message" %>">
                    <%= message %>
                </div>
            <% } %>
            
            <div class="vehicle-info">
                <h3>🚗 正在编辑车辆 ID: <%= vehicleId %></h3>
                <p>请修改下方的车辆信息，然后点击"更新车辆信息"按钮保存更改。</p>
            </div>
            
            <form method="post" action="editVehicle.jsp?id=<%= vehicleId %>">
                <div class="form-row">
                    <div class="form-group">
                        <label for="licensePlate">车牌号 <span class="required">*</span></label>
                        <input type="text" id="licensePlate" name="licensePlate" 
                               value="<%= licensePlate %>" placeholder="例如：京A12345" required>
                        <div class="form-help">请输入完整的车牌号码</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="status">车辆状态</label>
                        <select id="status" name="status">
                            <option value="available" <%= "available".equals(status) ? "selected" : "" %>>可用</option>
                            <option value="rented" <%= "rented".equals(status) ? "selected" : "" %>>已租出</option>
                            <option value="maintenance" <%= "maintenance".equals(status) ? "selected" : "" %>>维修中</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="brand">品牌 <span class="required">*</span></label>
                        <input type="text" id="brand" name="brand" 
                               value="<%= brand %>" placeholder="例如：丰田" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="model">型号 <span class="required">*</span></label>
                        <input type="text" id="model" name="model" 
                               value="<%= model %>" placeholder="例如：凯美瑞" required>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="purchaseDate">购买日期</label>
                        <input type="date" id="purchaseDate" name="purchaseDate" value="<%= purchaseDate %>">
                        <div class="form-help">选择车辆购买日期</div>
                    </div>
                    
                    <div class="form-group">
                        <label for="mileage">里程数（公里）</label>
                        <input type="number" id="mileage" name="mileage" min="0" 
                               value="<%= mileage %>" placeholder="例如：15000">
                        <div class="form-help">当前车辆的总里程数</div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">💾 更新车辆信息</button>
                    <a href="vehicles.jsp" class="btn btn-secondary">📋 返回车辆列表</a>
                    <a href="index.jsp" class="btn btn-secondary">🏠 返回首页</a>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const licensePlate = document.getElementById('licensePlate').value.trim();
            const brand = document.getElementById('brand').value.trim();
            const model = document.getElementById('model').value.trim();
            
            if (!licensePlate || !brand || !model) {
                e.preventDefault();
                alert('请填写所有必填字段（车牌号、品牌、型号）');
                return false;
            }
        });
        
        // 自动格式化车牌号
        document.getElementById('licensePlate').addEventListener('input', function(e) {
            this.value = this.value.toUpperCase();
        });
    </script>
</body>
</html>
