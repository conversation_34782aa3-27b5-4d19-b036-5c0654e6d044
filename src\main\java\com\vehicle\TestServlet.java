package com.vehicle;

import com.vehicle.dao.VehicleDaoFactory;
import com.vehicle.model.Vehicle;
import javax.servlet.*;
import javax.servlet.http.*;
import java.io.*;
import java.sql.*;
import java.util.*;

public class TestServlet extends HttpServlet {
    
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
            
        response.setContentType("text/html;charset=UTF-8");
        PrintWriter out = response.getWriter();
        
        out.println("<!DOCTYPE html>");
        out.println("<html>");
        out.println("<head>");
        out.println("<title>车辆管理系统验证</title>");
        out.println("<style>");
        out.println("body { font-family: Arial, sans-serif; margin: 40px; }");
        out.println("table { border-collapse: collapse; width: 100%; }");
        out.println("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }");
        out.println("th { background-color: #f2f2f2; }");
        out.println(".success { color: green; }");
        out.println(".error { color: red; }");
        out.println("</style>");
        out.println("</head>");
        out.println("<body>");
        
        out.println("<h1>车辆管理系统验证</h1>");
        
        try {
            // 测试数据库连接
            out.println("<h2>数据库连接测试</h2>");
            testDBConnection(out);
            
            // 显示车辆数据
            out.println("<h2>车辆列表</h2>");
            showVehicleData(out);
            
        } catch (SQLException e) {
            out.println("<h2 class='error'>数据库操作错误</h2>");
            out.println("<pre>");
            e.printStackTrace(out);
            out.println("</pre>");
        }
        
        out.println("</body>");
        out.println("</html>");
    }
    
    private void testDBConnection(PrintWriter out) throws SQLException {
        try (Connection conn = com.vehicle.util.DBUtil.getConnection()) {
            out.println("<p class='success'>✅ 数据库连接成功！</p>");
            out.println("<p><strong>SQL Server 版本:</strong> " + 
                conn.getMetaData().getDatabaseProductVersion() + "</p>");
        } catch (SQLException e) {
            out.println("<p class='error'>❌ 数据库连接失败: " + e.getMessage() + "</p>");
            throw e;
        }
    }
    
    private void showVehicleData(PrintWriter out) throws SQLException {
        List<Vehicle> vehicles = VehicleDaoFactory.getVehicleDao().getAllVehicles();
        
        if (vehicles.isEmpty()) {
            out.println("<p>没有找到任何车辆记录</p>");
            return;
        }
        
        out.println("<table>");
        out.println("<tr><th>ID</th><th>车牌号</th><th>型号</th><th>购买日期</th><th>状态</th></tr>");
        
        for (Vehicle vehicle : vehicles) {
            out.println("<tr>");
            out.println("<td>" + vehicle.getId() + "</td>");
            out.println("<td>" + vehicle.getLicensePlate() + "</td>");
            out.println("<td>" + vehicle.getModel() + "</td>");
            out.println("<td>" + vehicle.getPurchaseDate() + "</td>");
            out.println("<td>" + vehicle.getStatus() + "</td>");
            out.println("</tr>");
        }
        
        out.println("</table>");
    }
}