package com.vehicle.dao;

import com.vehicle.model.Vehicle;
import java.sql.SQLException;
import java.util.List;

public interface VehicleDAO {
    List<Vehicle> getAllVehicles() throws SQLException;

    /**
     * 根据关键词搜索车辆
     * @param keyword 搜索关键词（车牌号、品牌、型号）
     * @return 匹配的车辆列表
     * @throws SQLException 数据库操作异常
     */
    List<Vehicle> searchVehicles(String keyword) throws SQLException;

    /**
     * 根据车辆状态获取车辆列表
     * @param status 车辆状态
     * @return 指定状态的车辆列表
     * @throws SQLException 数据库操作异常
     */
    List<Vehicle> getVehiclesByStatus(String status) throws SQLException;
}