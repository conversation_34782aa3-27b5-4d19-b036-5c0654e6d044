<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.vehicle.dao.VehicleDAO" %>
<%@ page import="com.vehicle.dao.VehicleDaoImpl" %>
<%@ page import="com.vehicle.model.Vehicle" %>
<%@ page import="java.util.List" %>
<%@ page import="java.sql.SQLException" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>搜索调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
    </style>
</head>
<body>
    <h1>🔍 搜索功能调试页面</h1>
    
    <%
        String searchKeyword = request.getParameter("search");
        if (searchKeyword == null) {
            searchKeyword = "维修";
        }
    %>
    
    <div class="test-result info">
        <h3>📋 测试参数</h3>
        <p>搜索关键词: "<%= searchKeyword %>"</p>
        <p>关键词长度: <%= searchKeyword.length() %></p>
        <p>关键词字节: <%= java.util.Arrays.toString(searchKeyword.getBytes("UTF-8")) %></p>
    </div>
    
    <%
        VehicleDAO dao = new VehicleDaoImpl();
        
        try {
            // 测试搜索功能
            List<Vehicle> searchResults = dao.searchVehicles(searchKeyword);
    %>
            <div class="test-result success">
                <h3>✅ 搜索结果</h3>
                <p>找到 <%= searchResults.size() %> 条结果:</p>
                <% if (searchResults.size() > 0) { %>
                    <table border="1" style="border-collapse: collapse; width: 100%;">
                        <tr>
                            <th>ID</th>
                            <th>车牌号</th>
                            <th>品牌</th>
                            <th>型号</th>
                            <th>状态</th>
                        </tr>
                        <% for (Vehicle v : searchResults) { %>
                            <tr>
                                <td><%= v.getId() %></td>
                                <td><%= v.getLicensePlate() %></td>
                                <td><%= v.getBrand() %></td>
                                <td><%= v.getModel() %></td>
                                <td><%= v.getStatus() %></td>
                            </tr>
                        <% } %>
                    </table>
                <% } else { %>
                    <p style="color: orange;">没有找到匹配的车辆</p>
                <% } %>
            </div>
    <%
        } catch (SQLException e) {
    %>
            <div class="test-result error">
                <h3>❌ 搜索失败</h3>
                <p>错误信息: <%= e.getMessage() %></p>
                <p>错误详情: <%= e.toString() %></p>
            </div>
    <%
        }
        
        // 测试获取所有车辆
        try {
            List<Vehicle> allVehicles = dao.getAllVehicles();
    %>
            <div class="test-result info">
                <h3>📊 所有车辆数据</h3>
                <p>总共 <%= allVehicles.size() %> 辆车:</p>
                <table border="1" style="border-collapse: collapse; width: 100%;">
                    <tr>
                        <th>ID</th>
                        <th>车牌号</th>
                        <th>品牌</th>
                        <th>型号</th>
                        <th>状态</th>
                    </tr>
                    <% for (Vehicle v : allVehicles) { %>
                        <tr>
                            <td><%= v.getId() %></td>
                            <td><%= v.getLicensePlate() %></td>
                            <td><%= v.getBrand() %></td>
                            <td><%= v.getModel() %></td>
                            <td><%= v.getStatus() %></td>
                        </tr>
                    <% } %>
                </table>
            </div>
    <%
        } catch (SQLException e) {
    %>
            <div class="test-result error">
                <h3>❌ 获取所有车辆失败</h3>
                <p>错误信息: <%= e.getMessage() %></p>
            </div>
    <%
        }
    %>
    
    <hr>
    <h3>🧪 测试不同搜索关键词</h3>
    <p>
        <a href="?search=维修">搜索"维修"</a> |
        <a href="?search=丰田">搜索"丰田"</a> |
        <a href="?search=京A">搜索"京A"</a> |
        <a href="?search=可用">搜索"可用"</a> |
        <a href="?search=">搜索空字符串</a>
    </p>
    
    <p><a href="vehicle">返回车辆列表</a> | <a href="index.jsp">返回首页</a></p>
</body>
</html>
