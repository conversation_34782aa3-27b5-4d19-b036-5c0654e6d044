# 🚀 车辆管理系统 - 快速部署指南

## 📦 **部署包内容**

```
课程设计交付包/
├── 应用程序/
│   ├── vehicle-system.war          # Web应用程序包
│   └── 源代码/                     # 完整源代码
├── 数据库初始化脚本/
│   ├── 01_创建数据库和用户.sql
│   ├── 02_创建表结构.sql
│   ├── 03_插入测试数据.sql
│   └── 一键初始化.bat
├── 部署说明/
│   ├── 系统要求.md
│   ├── 快速部署指南.md
│   └── 故障排除.md
└── 课程设计文档/
    └── 车辆管理系统设计报告.md
```

## ⚡ **一键部署 (推荐)**

### 前提条件
✅ 已安装 JDK 8+  
✅ 已安装 SQL Server  
✅ 已安装 Apache Tomcat  

### 部署步骤

#### 1️⃣ **初始化数据库**
```bash
# 进入数据库脚本目录
cd 课程设计交付包/数据库初始化脚本/

# 运行一键初始化脚本
一键初始化.bat
```

#### 2️⃣ **部署Web应用**
```bash
# 复制WAR文件到Tomcat
copy "课程设计交付包/应用程序/vehicle-system.war" "%CATALINA_HOME%/webapps/"

# 启动Tomcat
%CATALINA_HOME%/bin/startup.bat
```

#### 3️⃣ **访问系统**
打开浏览器访问：http://localhost:8080/vehicle/

## 🔧 **详细部署步骤**

### 步骤1: 环境准备

#### 安装JDK
1. 下载并安装 JDK 8 或更高版本
2. 配置环境变量：
   ```
   JAVA_HOME = C:\Program Files\Java\jdk1.8.0_xxx
   PATH = %JAVA_HOME%\bin;%PATH%
   ```
3. 验证安装：`java -version`

#### 安装SQL Server
1. 下载并安装 SQL Server 2017+
2. 启用混合身份验证模式
3. 启用TCP/IP协议
4. 确保服务正在运行

#### 安装Tomcat
1. 下载 Apache Tomcat 8.5+
2. 解压到目录（如：C:\apache-tomcat-9.0.xx）
3. 配置环境变量：
   ```
   CATALINA_HOME = C:\apache-tomcat-9.0.xx
   ```

### 步骤2: 数据库配置

#### 方法A: 使用一键脚本 (推荐)
```bash
cd 数据库初始化脚本
一键初始化.bat
```

#### 方法B: 手动执行
```sql
-- 1. 在SSMS中连接到SQL Server
-- 2. 依次执行以下脚本：
sqlcmd -S localhost -E -i "01_创建数据库和用户.sql"
sqlcmd -S localhost -E -i "02_创建表结构.sql"
sqlcmd -S localhost -E -i "03_插入测试数据.sql"
```

### 步骤3: 应用部署

#### 部署WAR文件
```bash
# 复制WAR文件
copy vehicle-system.war %CATALINA_HOME%\webapps\

# 启动Tomcat
%CATALINA_HOME%\bin\startup.bat
```

#### 验证部署
1. 检查Tomcat日志：`%CATALINA_HOME%\logs\catalina.out`
2. 确认应用已部署：访问 http://localhost:8080/manager/html
3. 测试应用：访问 http://localhost:8080/vehicle/

## 🎯 **配置说明**

### 数据库连接配置
应用默认使用以下数据库配置：
```
服务器: localhost
数据库: vehicle_db
用户名: vehicle_user
密码: Vehicle123!
```

### 端口配置
- **Web服务**: 8080 (Tomcat默认)
- **数据库**: 1433 (SQL Server默认)

### 修改配置 (如需要)
如需修改数据库连接，编辑源代码中的：
`src/main/java/com/vehicle/util/DBUtil.java`

## ✅ **部署验证**

### 1. 数据库验证
```sql
-- 连接到数据库
sqlcmd -S localhost -U vehicle_user -P Vehicle123!

-- 查询测试数据
USE vehicle_db;
SELECT COUNT(*) FROM vehicle;
```

### 2. Web应用验证
访问以下页面确认功能正常：
- 主页：http://localhost:8080/vehicle/
- 车辆列表：http://localhost:8080/vehicle/vehicles.jsp
- 添加车辆：http://localhost:8080/vehicle/addVehicle.jsp

### 3. 功能测试
- ✅ 查看车辆列表
- ✅ 添加新车辆
- ✅ 编辑车辆信息
- ✅ 删除车辆记录
- ✅ 搜索车辆

## 🚨 **常见问题**

### 问题1: 数据库连接失败
**解决方案**：
1. 检查SQL Server服务是否启动
2. 确认TCP/IP协议已启用
3. 验证用户名密码正确
4. 检查防火墙设置

### 问题2: Tomcat启动失败
**解决方案**：
1. 检查JDK是否正确安装
2. 确认JAVA_HOME环境变量
3. 检查端口8080是否被占用
4. 查看Tomcat日志文件

### 问题3: 页面无法访问
**解决方案**：
1. 确认Tomcat已启动
2. 检查WAR文件是否正确部署
3. 验证URL地址正确
4. 清除浏览器缓存

## 📞 **技术支持**

如遇到部署问题，请检查：
1. 系统要求是否满足
2. 部署步骤是否正确执行
3. 错误日志信息
4. 网络和防火墙配置

详细故障排除请参考：`故障排除.md`
