# 🔧 车辆管理系统 - 故障排除指南

## 🚨 **常见问题及解决方案**

### 1. 数据库连接问题

#### 问题：用户 'vehicle_user' 登录失败
**错误信息**：`用户 'vehicle_user' 登录失败`

**解决方案**：
```sql
-- 1. 检查用户是否存在
USE master;
SELECT name FROM sys.server_principals WHERE name = 'vehicle_user';

-- 2. 重新创建用户
DROP LOGIN vehicle_user;
CREATE LOGIN vehicle_user WITH PASSWORD = 'Vehicle123!';

-- 3. 分配数据库权限
USE vehicle_db;
CREATE USER vehicle_user FOR LOGIN vehicle_user;
ALTER ROLE db_owner ADD MEMBER vehicle_user;
```

#### 问题：无法连接到SQL Server
**错误信息**：`无法连接到服务器`

**解决方案**：
1. **检查SQL Server服务**
   ```cmd
   services.msc
   # 确保 SQL Server (MSSQLSERVER) 服务正在运行
   ```

2. **启用TCP/IP协议**
   - 打开 SQL Server Configuration Manager
   - 展开 "SQL Server Network Configuration"
   - 点击 "Protocols for MSSQLSERVER"
   - 右键 "TCP/IP" → "Enable"
   - 重启SQL Server服务

3. **检查端口配置**
   ```cmd
   netstat -an | findstr 1433
   # 应该看到 0.0.0.0:1433 LISTENING
   ```

#### 问题：数据库不存在
**错误信息**：`Cannot open database "vehicle_db"`

**解决方案**：
```sql
-- 重新创建数据库
USE master;
CREATE DATABASE vehicle_db;
```

### 2. Web应用部署问题

#### 问题：Tomcat启动失败
**错误信息**：`JAVA_HOME environment variable is not defined correctly`

**解决方案**：
1. **检查JDK安装**
   ```cmd
   java -version
   javac -version
   ```

2. **配置环境变量**
   ```cmd
   set JAVA_HOME=C:\Program Files\Java\jdk1.8.0_xxx
   set PATH=%JAVA_HOME%\bin;%PATH%
   ```

3. **验证配置**
   ```cmd
   echo %JAVA_HOME%
   echo %PATH%
   ```

#### 问题：端口8080被占用
**错误信息**：`Address already in use: bind`

**解决方案**：
1. **查找占用进程**
   ```cmd
   netstat -ano | findstr 8080
   taskkill /PID <进程ID> /F
   ```

2. **修改Tomcat端口**
   编辑 `%CATALINA_HOME%\conf\server.xml`：
   ```xml
   <Connector port="8081" protocol="HTTP/1.1" />
   ```

#### 问题：WAR文件部署失败
**错误信息**：应用无法访问

**解决方案**：
1. **检查WAR文件**
   ```cmd
   # 确保WAR文件在webapps目录
   dir %CATALINA_HOME%\webapps\vehicle-system.war
   ```

2. **检查部署日志**
   ```cmd
   type %CATALINA_HOME%\logs\catalina.out
   ```

3. **手动解压部署**
   ```cmd
   cd %CATALINA_HOME%\webapps
   jar -xf vehicle-system.war
   ```

### 3. 应用运行问题

#### 问题：页面显示数据库连接错误
**错误信息**：页面显示红色错误提示

**解决方案**：
1. **检查数据库配置**
   验证 `DBUtil.java` 中的连接参数：
   ```java
   private static final String URL = "********************************************************************************************";
   private static final String USER = "vehicle_user";
   private static final String PASSWORD = "Vehicle123!";
   ```

2. **测试数据库连接**
   ```cmd
   sqlcmd -S localhost -U vehicle_user -P Vehicle123! -Q "SELECT @@VERSION"
   ```

3. **检查防火墙**
   ```cmd
   # 添加防火墙规则
   netsh advfirewall firewall add rule name="SQL Server" dir=in action=allow protocol=TCP localport=1433
   ```

#### 问题：页面显示空白或404错误
**解决方案**：
1. **检查URL路径**
   正确的访问地址：`http://localhost:8080/vehicle/`

2. **清除浏览器缓存**
   - Ctrl + F5 强制刷新
   - 清除浏览器缓存和Cookie

3. **检查Tomcat状态**
   访问：`http://localhost:8080/manager/html`

### 4. 性能问题

#### 问题：页面加载缓慢
**解决方案**：
1. **增加Tomcat内存**
   编辑 `%CATALINA_HOME%\bin\catalina.bat`：
   ```cmd
   set JAVA_OPTS=-Xms512m -Xmx1024m
   ```

2. **优化数据库连接**
   - 检查数据库索引
   - 优化SQL查询语句

3. **检查网络连接**
   ```cmd
   ping localhost
   telnet localhost 8080
   ```

## 🔍 **诊断工具**

### 1. 日志文件位置
- **Tomcat日志**：`%CATALINA_HOME%\logs\`
- **SQL Server日志**：SQL Server Management Studio → Management → SQL Server Logs

### 2. 测试命令
```cmd
# 测试Java环境
java -version

# 测试数据库连接
sqlcmd -S localhost -E -Q "SELECT @@VERSION"

# 测试Web服务
curl http://localhost:8080/vehicle/

# 检查端口状态
netstat -an | findstr "8080\|1433"
```

### 3. 系统信息收集
```cmd
# 系统信息
systeminfo

# 服务状态
sc query MSSQLSERVER
sc query Tomcat9

# 进程信息
tasklist | findstr "java\|sqlservr"
```

## 📞 **获取帮助**

### 收集错误信息
在寻求帮助时，请提供：
1. **错误截图**
2. **完整错误信息**
3. **系统环境信息**
4. **操作步骤**

### 日志分析
重要日志文件：
- `catalina.out` - Tomcat运行日志
- `localhost.log` - 应用访问日志
- SQL Server错误日志

### 联系方式
- 查看课程设计文档
- 参考在线文档
- 技术论坛求助

## ✅ **预防措施**

### 定期维护
1. **备份数据库**
   ```sql
   BACKUP DATABASE vehicle_db TO DISK = 'C:\backup\vehicle_db.bak'
   ```

2. **清理日志文件**
   定期清理Tomcat日志文件

3. **监控系统资源**
   - CPU使用率
   - 内存使用情况
   - 磁盘空间

### 安全建议
1. **修改默认密码**
2. **限制网络访问**
3. **定期更新软件版本**
4. **配置防火墙规则**
