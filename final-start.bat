@echo off
setlocal

REM 完全清除环境变量
set "PATH="
set "CLASSPATH="
set "JAVA_HOME="
set "MAVEN_HOME="
set "CONDA_DEFAULT_ENV="
set "CONDA_EXE="
set "CONDA_PREFIX="
set "CONDA_PROMPT_MODIFIER="
set "CONDA_PYTHON_EXE="
set "CONDA_ROOT="
set "CONDA_SHLVL="
set "PYTHONPATH="

REM 设置最小PATH
set "PATH=C:\Windows\System32;C:\Windows;C:\Windows\System32\Wbem"

REM 设置Java
set "JAVA_HOME=D:\javaJDK8"
set "PATH=%JAVA_HOME%\bin;%PATH%"

echo ========================================
echo Vehicle Management System
echo ========================================
echo.
echo JAVA_HOME: %JAVA_HOME%
echo PATH: %PATH%
echo.

REM 测试Java
echo Testing Java...
"%JAVA_HOME%\bin\java.exe" -version
if errorlevel 1 (
    echo ERROR: Java test failed
    pause
    exit /b 1
)
echo Java OK
echo.

REM 切换目录
cd /d "D:\projects\vehicle-system"
echo Current directory: %CD%
echo.

echo Starting Maven project...
echo URL: http://localhost:8080/vehicle
echo Press Ctrl+C to stop
echo ========================================
echo.

REM 直接使用Maven完整路径，不依赖PATH
"D:\XunLai\apache-maven-3.9.10-bin\apache-maven-3.9.10\bin\mvn.cmd" compile tomcat7:run

echo.
echo Project stopped.
pause
