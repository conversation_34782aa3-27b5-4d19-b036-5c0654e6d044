# Vehicle Management System Launcher
Write-Host "========================================" -ForegroundColor Green
Write-Host "Vehicle Management System" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green

# Set environment variables
$env:PATH = "C:\Windows\System32;C:\Windows;C:\Windows\System32\Wbem"
$env:JAVA_HOME = "D:\javaJDK8"
$env:PATH = "$env:JAVA_HOME\bin;$env:PATH"
$env:MAVEN_HOME = "D:\XunLai\apache-maven-3.9.10-bin\apache-maven-3.9.10"
$env:PATH = "$env:MAVEN_HOME\bin;$env:PATH"

# Change to project directory
Set-Location "D:\projects\vehicle-system"

Write-Host "Starting project at http://localhost:8080/vehicle" -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green

# Start Maven
& mvn compile tomcat7:run

Write-Host "Project stopped." -ForegroundColor Yellow
