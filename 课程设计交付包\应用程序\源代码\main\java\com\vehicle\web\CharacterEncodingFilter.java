package com.vehicle.web;

import javax.servlet.*;
import java.io.IOException;

public class CharacterEncodingFilter implements Filter {
    
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 初始化过滤器时执行，可用于加载资源或配置
        // 例如获取 web.xml 中的初始化参数
        String encoding = filterConfig.getInitParameter("encoding");
        if (encoding != null) {
            // 可以设置编码格式作为上下文属性供其他组件使用
            filterConfig.getServletContext().setAttribute("encoding", encoding);
        }
    }
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        // 设置请求和响应的字符编码
        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html;charset=UTF-8");
        
        // 继续执行过滤器链
        chain.doFilter(request, response);
    }
    
    @Override
    public void destroy() {
        // 过滤器销毁时执行，用于释放资源
        // 例如清理缓存、关闭连接等操作
    }
}