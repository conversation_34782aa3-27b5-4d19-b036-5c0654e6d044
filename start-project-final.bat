@echo off
title Vehicle Management System

echo ========================================
echo 车辆管理系统启动器
echo ========================================
echo.

REM 完全禁用conda
set CONDA_DEFAULT_ENV=
set CONDA_EXE=
set CONDA_PREFIX=
set CONDA_PROMPT_MODIFIER=
set CONDA_PYTHON_EXE=
set CONDA_ROOT=
set CONDA_SHLVL=
set PYTHONPATH=
set PYTHONIOENCODING=

REM 设置最小化的PATH，避免conda干扰
set "PATH=C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem"

REM 设置Java和Maven的完整路径
set "JAVA_HOME=D:\javaJDK8"
set "MAVEN_HOME=D:\XunLai\apache-maven-3.9.10-bin\apache-maven-3.9.10"

echo 正在检查Java...
if exist "%JAVA_HOME%\bin\java.exe" (
    echo Java找到: %JAVA_HOME%\bin\java.exe
    "%JAVA_HOME%\bin\java.exe" -version
    if errorlevel 1 (
        echo 错误：Java测试失败！
        pause
        exit /b 1
    )
) else (
    echo 错误：Java未找到！路径：%JAVA_HOME%\bin\java.exe
    pause
    exit /b 1
)

echo.
echo 正在检查Maven...
if exist "%MAVEN_HOME%\bin\mvn.cmd" (
    echo Maven找到: %MAVEN_HOME%\bin\mvn.cmd
    "%MAVEN_HOME%\bin\mvn.cmd" -version
    if errorlevel 1 (
        echo 错误：Maven测试失败！
        pause
        exit /b 1
    )
) else (
    echo 错误：Maven未找到！路径：%MAVEN_HOME%\bin\mvn.cmd
    pause
    exit /b 1
)

echo.
echo 切换到项目目录...
cd /d "D:\projects\vehicle-system"
if errorlevel 1 (
    echo 错误：无法找到项目目录！
    pause
    exit /b 1
)

echo 当前目录：%CD%

echo.
echo 检查pom.xml...
if exist "pom.xml" (
    echo pom.xml文件存在
) else (
    echo 错误：pom.xml文件不存在！
    pause
    exit /b 1
)

echo.
echo ========================================
echo 开始启动项目...
echo ========================================
echo 项目将在以下地址启动：
echo   http://localhost:8080/vehicle
echo.
echo 按 Ctrl+C 停止服务器
echo ========================================
echo.

REM 使用完整路径启动Maven
"%MAVEN_HOME%\bin\mvn.cmd" clean compile tomcat7:run

echo.
echo 项目已停止运行。
pause
