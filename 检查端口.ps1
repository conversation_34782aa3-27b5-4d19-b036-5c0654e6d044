# 🔍 PowerShell脚本：检查SQL Server端口

Write-Host "🔍 检查SQL Server端口信息" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# 1. 检查SQL Server服务
Write-Host "`n📋 1. SQL Server服务状态：" -ForegroundColor Yellow
Get-Service -Name "*SQL*" | Where-Object {$_.Status -eq "Running"} | Format-Table Name, Status, StartType

# 2. 检查端口占用情况
Write-Host "`n📋 2. 检查端口1433：" -ForegroundColor Yellow
$port1433 = Get-NetTCPConnection -LocalPort 1433 -ErrorAction SilentlyContinue
if ($port1433) {
    Write-Host "✅ 端口1433正在使用" -ForegroundColor Green
    $port1433 | Format-Table LocalAddress, LocalPort, State, OwningProcess
} else {
    Write-Host "❌ 端口1433未被占用" -ForegroundColor Red
}

# 3. 检查所有SQL Server相关端口
Write-Host "`n📋 3. SQL Server相关端口：" -ForegroundColor Yellow
Get-NetTCPConnection | Where-Object {$_.OwningProcess -in (Get-Process -Name "*sql*" -ErrorAction SilentlyContinue).Id} | Format-Table LocalAddress, LocalPort, State

# 4. 测试端口连接
Write-Host "`n📋 4. 测试端口连接：" -ForegroundColor Yellow
$testPorts = @(1433, 1434)
foreach ($port in $testPorts) {
    $result = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
    if ($result.TcpTestSucceeded) {
        Write-Host "✅ 端口 $port 可以连接" -ForegroundColor Green
    } else {
        Write-Host "❌ 端口 $port 无法连接" -ForegroundColor Red
    }
}

# 5. 检查防火墙规则
Write-Host "`n📋 5. 防火墙规则（SQL Server相关）：" -ForegroundColor Yellow
Get-NetFirewallRule -DisplayName "*SQL*" | Where-Object {$_.Enabled -eq "True"} | Format-Table DisplayName, Direction, Action

# 6. 从注册表读取端口配置
Write-Host "`n📋 6. 注册表中的端口配置：" -ForegroundColor Yellow
try {
    $regPath = "HKLM:\SOFTWARE\Microsoft\Microsoft SQL Server\MSSQL*\MSSQLServer\SuperSocketNetLib\Tcp\IPAll"
    $tcpPort = Get-ItemProperty -Path $regPath -Name "TcpPort" -ErrorAction SilentlyContinue
    $dynamicPorts = Get-ItemProperty -Path $regPath -Name "TcpDynamicPorts" -ErrorAction SilentlyContinue
    
    if ($tcpPort) {
        Write-Host "静态端口: $($tcpPort.TcpPort)" -ForegroundColor Green
    }
    if ($dynamicPorts) {
        Write-Host "动态端口: $($dynamicPorts.TcpDynamicPorts)" -ForegroundColor Green
    }
} catch {
    Write-Host "无法读取注册表信息" -ForegroundColor Red
}

Write-Host "`n🔧 建议的解决步骤：" -ForegroundColor Cyan
Write-Host "1. 确保SQL Server服务正在运行" -ForegroundColor White
Write-Host "2. 在SQL Server Configuration Manager中启用TCP/IP" -ForegroundColor White
Write-Host "3. 设置固定端口1433" -ForegroundColor White
Write-Host "4. 重启SQL Server服务" -ForegroundColor White
Write-Host "5. 配置防火墙允许端口1433" -ForegroundColor White
