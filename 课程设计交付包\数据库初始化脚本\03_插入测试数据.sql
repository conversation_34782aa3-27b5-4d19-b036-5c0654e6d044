-- =====================================================
-- 车辆管理系统 - 测试数据插入脚本
-- 课程设计项目
-- 创建时间: 2025-06-17
-- =====================================================

-- 确保使用正确的数据库
USE vehicle_db;
GO

-- 清空现有数据
DELETE FROM vehicle;
DBCC CHECKIDENT ('vehicle', RESEED, 0);
PRINT '已清空现有数据';
GO

-- 插入测试数据
INSERT INTO vehicle (license_plate, brand, model, purchase_date, mileage, status) VALUES
('京A12345', '丰田', '凯美瑞', '2023-01-15', 15000, '可用'),
('京B67890', '本田', '雅阁', '2022-06-20', 25000, '可用'),
('京C11111', '大众', '帕萨特', '2023-03-10', 8000, '维修中'),
('沪D22222', '奔驰', 'C200', '2023-05-01', 5000, '可用'),
('粤E33333', '宝马', '320i', '2022-12-15', 18000, '可用'),
('津F44444', '奥迪', 'A4L', '2023-02-28', 12000, '可用'),
('苏G55555', '比亚迪', '秦PLUS', '2023-04-10', 3000, '可用'),
('浙H66666', '特斯拉', 'Model 3', '2023-01-20', 8500, '维修中'),
('鲁I77777', '吉利', '博越', '2022-11-05', 22000, '可用'),
('川J88888', '长城', '哈弗H6', '2023-03-25', 6500, '可用'),
('辽K99999', '福特', '蒙迪欧', '2022-08-12', 28000, '可用'),
('黑L00000', '日产', '天籁', '2023-02-14', 9800, '可用'),
('湘M11111', '现代', '索纳塔', '2022-10-30', 19500, '维修中'),
('鄂N22222', '起亚', 'K5', '2023-04-05', 4200, '可用'),
('赣O33333', '雪佛兰', '迈锐宝', '2022-09-18', 24500, '可用');
GO

-- 验证数据插入
SELECT COUNT(*) as '总车辆数' FROM vehicle;
SELECT status as '状态', COUNT(*) as '数量' FROM vehicle GROUP BY status;
GO

PRINT '=== 测试数据插入完成 ===';
PRINT '共插入 15 条车辆记录';
GO

-- 显示插入的数据
SELECT 
    id as '编号',
    license_plate as '车牌号',
    brand as '品牌',
    model as '型号',
    purchase_date as '购买日期',
    mileage as '里程(公里)',
    status as '状态'
FROM vehicle 
ORDER BY id;
GO
