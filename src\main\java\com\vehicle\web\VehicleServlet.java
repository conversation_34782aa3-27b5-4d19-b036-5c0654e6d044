package com.vehicle.web;

import com.vehicle.dao.VehicleDAO;
import com.vehicle.dao.VehicleDaoFactory;
import com.vehicle.model.Vehicle;
import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.annotation.WebServlet;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

@WebServlet("/vehicles")
public class VehicleServlet extends HttpServlet {
    
    private VehicleDAO vehicleDao;
    
    @Override
    public void init() {
        vehicleDao = (VehicleDAO) VehicleDaoFactory.getVehicleDao();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            // 获取搜索参数
            String searchKeyword = request.getParameter("search");
            String statusFilter = request.getParameter("status");

            List<Vehicle> vehicles;

            // 根据参数决定查询方式
            if (searchKeyword != null && !searchKeyword.trim().isEmpty()) {
                // 执行搜索
                vehicles = vehicleDao.searchVehicles(searchKeyword);
                request.setAttribute("searchKeyword", searchKeyword);
                request.setAttribute("message", "搜索关键词: \"" + searchKeyword + "\" - 找到 " + vehicles.size() + " 条结果");
            } else if (statusFilter != null && !statusFilter.trim().isEmpty()) {
                // 按状态筛选
                vehicles = vehicleDao.getVehiclesByStatus(statusFilter);
                request.setAttribute("statusFilter", statusFilter);
                request.setAttribute("message", "状态筛选: \"" + statusFilter + "\" - 找到 " + vehicles.size() + " 条结果");
            } else {
                // 获取所有车辆
                vehicles = vehicleDao.getAllVehicles();
                request.setAttribute("message", "显示所有车辆 - 共 " + vehicles.size() + " 条记录");
            }

            request.setAttribute("vehicles", vehicles);

            // 转发到JSP页面
            RequestDispatcher dispatcher = request.getRequestDispatcher("/vehicles.jsp");
            dispatcher.forward(request, response);

        } catch (SQLException e) {
            e.printStackTrace();
            request.setAttribute("error", "数据库操作失败: " + e.getMessage());
            RequestDispatcher dispatcher = request.getRequestDispatcher("/vehicles.jsp");
            dispatcher.forward(request, response);
        }
    }
}