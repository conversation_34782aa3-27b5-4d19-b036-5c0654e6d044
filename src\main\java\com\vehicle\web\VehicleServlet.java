package com.vehicle.web;

import com.vehicle.dao.VehicleDAO;
import com.vehicle.dao.VehicleDaoImpl;
import com.vehicle.model.Vehicle;
import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.annotation.WebServlet;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

@WebServlet({"/vehicle", "/vehicles"})
public class VehicleServlet extends HttpServlet {
    
    private VehicleDAO vehicleDao;
    
    @Override
    public void init() {
        vehicleDao = new VehicleDaoImpl();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        try {
            // 设置请求编码
            request.setCharacterEncoding("UTF-8");

            // 获取搜索参数
            String searchKeyword = request.getParameter("search");
            String statusFilter = request.getParameter("status");

            // 调试字符编码
            if (searchKeyword != null) {
                System.out.println("原始搜索关键词: " + searchKeyword);
                System.out.println("关键词字节数组: " + java.util.Arrays.toString(searchKeyword.getBytes("UTF-8")));
                System.out.println("关键词长度: " + searchKeyword.length());
            }

            List<Vehicle> vehicles;

            // 根据参数决定查询方式
            if (searchKeyword != null && !searchKeyword.trim().isEmpty()) {
                // 执行搜索
                System.out.println("VehicleServlet: 执行搜索，关键词: " + searchKeyword);
                vehicles = vehicleDao.searchVehicles(searchKeyword);
                System.out.println("VehicleServlet: 搜索结果数量: " + vehicles.size());
                request.setAttribute("searchKeyword", searchKeyword);
                request.setAttribute("message", "搜索关键词: \"" + searchKeyword + "\" - 找到 " + vehicles.size() + " 条结果");
            } else if (statusFilter != null && !statusFilter.trim().isEmpty()) {
                // 按状态筛选
                vehicles = vehicleDao.getVehiclesByStatus(statusFilter);
                request.setAttribute("statusFilter", statusFilter);
                request.setAttribute("message", "状态筛选: \"" + statusFilter + "\" - 找到 " + vehicles.size() + " 条结果");
            } else {
                // 获取所有车辆
                vehicles = vehicleDao.getAllVehicles();
                request.setAttribute("message", "显示所有车辆 - 共 " + vehicles.size() + " 条记录");
            }

            request.setAttribute("vehicles", vehicles);

            // 转发到JSP页面
            RequestDispatcher dispatcher = request.getRequestDispatcher("/vehicles.jsp");
            dispatcher.forward(request, response);

        } catch (SQLException e) {
            e.printStackTrace();
            request.setAttribute("error", "数据库操作失败: " + e.getMessage());
            RequestDispatcher dispatcher = request.getRequestDispatcher("/vehicles.jsp");
            dispatcher.forward(request, response);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // POST请求也使用相同的逻辑处理
        doGet(request, response);
    }
}