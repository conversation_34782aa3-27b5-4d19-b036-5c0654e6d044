package com.vehicle.web;

import com.vehicle.dao.VehicleDAO;
import com.vehicle.dao.VehicleDaoFactory;
import com.vehicle.model.Vehicle;
import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.annotation.WebServlet;
import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

@WebServlet("/vehicles")
public class VehicleServlet extends HttpServlet {
    
    private VehicleDAO vehicleDao;
    
    @Override
    public void init() {
        vehicleDao = (VehicleDAO) VehicleDaoFactory.getVehicleDao();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        
        try {
            List<Vehicle> vehicles = vehicleDao.getAllVehicles();
            request.setAttribute("vehicles", vehicles);
            
            // 转发到JSP页面
            RequestDispatcher dispatcher = request.getRequestDispatcher("/vehicleList.jsp");
            dispatcher.forward(request, response);
            
        } catch (SQLException e) {
            throw new ServletException("数据库操作失败", e);
        }
    }
}