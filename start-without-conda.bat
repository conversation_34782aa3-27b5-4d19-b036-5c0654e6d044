@echo off
title 车辆管理系统启动器

REM 完全绕过conda，使用干净的环境
set "PATH=C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0"

REM 设置Java和Maven
set "JAVA_HOME=D:\javaJDK8"
set "MAVEN_HOME=D:\XunLai\apache-maven-3.9.10-bin\apache-maven-3.9.10"
set "PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%"

REM 清除conda相关环境变量
set CONDA_DEFAULT_ENV=
set CONDA_EXE=
set CONDA_PREFIX=
set CONDA_PROMPT_MODIFIER=
set CONDA_PYTHON_EXE=
set CONDA_ROOT=
set CONDA_SHLVL=
set PYTHONPATH=

echo ========================================
echo 车辆管理系统启动器
echo ========================================
echo.

echo 检查Java环境...
java -version
if errorlevel 1 (
    echo 错误：Java未正确安装或配置！
    pause
    exit /b 1
)
echo.

echo 检查Maven环境...
mvn -version
if errorlevel 1 (
    echo 错误：Maven未正确安装或配置！
    pause
    exit /b 1
)
echo.

echo 切换到项目目录...
cd /d "D:\projects\vehicle-system"
if errorlevel 1 (
    echo 错误：无法找到项目目录！
    pause
    exit /b 1
)

echo 开始编译和启动项目...
echo 项目将在 http://localhost:8080/vehicle 启动
echo 按 Ctrl+C 停止服务器
echo.

mvn clean compile tomcat7:run

echo.
echo 项目已停止运行。
pause
