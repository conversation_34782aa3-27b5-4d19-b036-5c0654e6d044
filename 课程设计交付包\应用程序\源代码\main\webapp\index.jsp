<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>车辆管理系统 - 首页</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 50px 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 50px 30px;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        .feature-icon {
            font-size: 3em;
            margin-bottom: 20px;
            display: block;
        }

        .feature-title {
            font-size: 1.3em;
            font-weight: 600;
            margin-bottom: 15px;
            color: #495057;
        }

        .feature-desc {
            color: #6c757d;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .quick-actions {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
        }

        .quick-actions h2 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .system-info {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            border-left: 5px solid #2196f3;
        }

        .system-info h3 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .system-info p {
            color: #424242;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 车辆管理系统</h1>
            <p>专业的车辆信息管理平台 - 让车辆管理更简单高效</p>
        </div>

        <div class="content">
            <div class="features">
                <div class="feature-card">
                    <span class="feature-icon">📋</span>
                    <div class="feature-title">车辆列表管理</div>
                    <div class="feature-desc">查看所有车辆信息，支持搜索和筛选功能，实时统计车辆状态</div>
                    <a href="vehicles.jsp" class="btn btn-primary">查看车辆列表</a>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">➕</span>
                    <div class="feature-title">添加新车辆</div>
                    <div class="feature-desc">快速录入新车辆信息，包括车牌号、品牌型号、购买日期等详细信息</div>
                    <a href="addVehicle.jsp" class="btn btn-success">添加新车辆</a>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">🔧</span>
                    <div class="feature-title">系统测试</div>
                    <div class="feature-desc">验证数据库连接状态，查看系统运行情况和基础功能测试</div>
                    <a href="test" class="btn btn-info">系统测试</a>
                </div>
            </div>

            <div class="quick-actions">
                <h2>🚀 快速操作</h2>
                <div class="action-buttons">
                    <a href="vehicles.jsp" class="btn btn-primary">📊 车辆总览</a>
                    <a href="addVehicle.jsp" class="btn btn-success">🆕 快速添加</a>
                    <a href="test" class="btn btn-info">🔍 系统检查</a>
                </div>
            </div>

            <div class="system-info">
                <h3>💡 系统特色</h3>
                <p>
                    <strong>• 完整的CRUD操作：</strong> 支持车辆信息的增加、查询、修改、删除<br>
                    <strong>• 智能搜索功能：</strong> 可按车牌号、品牌、型号快速搜索<br>
                    <strong>• 状态管理：</strong> 实时跟踪车辆可用、已租出、维修中等状态<br>
                    <strong>• 数据统计：</strong> 自动统计各类车辆数量，提供数据概览<br>
                    <strong>• 响应式设计：</strong> 支持各种设备访问，界面美观易用
                </p>
            </div>
        </div>
    </div>

    <script>
        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    card.style.transition = 'all 0.6s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 200);
            });
        });
    </script>
</body>
</html>