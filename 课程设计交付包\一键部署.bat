@echo off
chcp 65001 >nul
title 车辆管理系统 - 一键部署脚本

echo.
echo =====================================================
echo 🚗 车辆管理系统 - 一键部署脚本
echo 课程设计项目 v1.0
echo =====================================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ❌ 错误：需要管理员权限运行此脚本
    echo 请右键点击脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo 🔍 正在检查系统环境...
echo.

:: 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未检测到Java环境
    echo 请先安装JDK 8或更高版本
    echo 下载地址：https://www.oracle.com/java/technologies/downloads/
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

:: 检查SQL Server
sqlcmd -S localhost -E -Q "SELECT @@VERSION" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：无法连接到SQL Server
    echo 请确保SQL Server已安装并正在运行
    echo 下载地址：https://www.microsoft.com/sql-server/sql-server-downloads
    pause
    exit /b 1
)
echo ✅ SQL Server连接检查通过

:: 检查Tomcat环境
if not defined CATALINA_HOME (
    echo ❌ 错误：未检测到Tomcat环境
    echo 请设置CATALINA_HOME环境变量
    echo 下载地址：https://tomcat.apache.org/download-90.cgi
    pause
    exit /b 1
)

if not exist "%CATALINA_HOME%\bin\startup.bat" (
    echo ❌ 错误：Tomcat安装路径无效
    echo CATALINA_HOME: %CATALINA_HOME%
    pause
    exit /b 1
)
echo ✅ Tomcat环境检查通过

echo.
echo 🎯 环境检查完成，开始部署...
echo.

:: 步骤1：初始化数据库
echo [1/3] 🗄️ 正在初始化数据库...
cd /d "%~dp0数据库初始化脚本"

echo   - 创建数据库和用户...
sqlcmd -S localhost -E -i "01_创建数据库和用户.sql" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 数据库创建失败，请检查SQL Server配置
    pause
    exit /b 1
)

echo   - 创建表结构...
sqlcmd -S localhost -E -i "02_创建表结构.sql" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 表结构创建失败
    pause
    exit /b 1
)

echo   - 插入测试数据...
sqlcmd -S localhost -E -i "03_插入测试数据.sql" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 测试数据插入失败
    pause
    exit /b 1
)
echo ✅ 数据库初始化完成

:: 步骤2：停止现有Tomcat服务
echo [2/3] 🛑 正在停止现有Tomcat服务...
taskkill /F /IM java.exe >nul 2>&1
timeout /t 2 >nul
echo ✅ Tomcat服务已停止

:: 步骤3：部署应用
echo [3/3] 📦 正在部署Web应用...
cd /d "%~dp0"

:: 删除现有部署
if exist "%CATALINA_HOME%\webapps\vehicle" (
    rmdir /s /q "%CATALINA_HOME%\webapps\vehicle" >nul 2>&1
)
if exist "%CATALINA_HOME%\webapps\vehicle.war" (
    del /q "%CATALINA_HOME%\webapps\vehicle.war" >nul 2>&1
)

:: 复制WAR文件
copy "应用程序\vehicle-system.war" "%CATALINA_HOME%\webapps\vehicle.war" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ WAR文件复制失败
    pause
    exit /b 1
)
echo ✅ 应用部署完成

:: 启动Tomcat
echo.
echo 🚀 正在启动Tomcat服务器...
start "" "%CATALINA_HOME%\bin\startup.bat"

echo.
echo ⏳ 等待服务器启动（约10秒）...
timeout /t 10 >nul

:: 检查服务是否启动成功
echo.
echo 🔍 正在检查服务状态...
curl -s http://localhost:8080/vehicle/ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 服务启动成功！
) else (
    echo ⚠️  服务可能还在启动中，请稍后访问
)

echo.
echo =====================================================
echo 🎉 部署完成！
echo.
echo 📋 系统信息：
echo   - 数据库：vehicle_db
echo   - 用户名：vehicle_user  
echo   - 密码：Vehicle123!
echo   - 测试数据：15条车辆记录
echo.
echo 🌐 访问地址：
echo   - 主页：http://localhost:8080/vehicle/
echo   - 车辆列表：http://localhost:8080/vehicle/vehicles.jsp
echo   - 添加车辆：http://localhost:8080/vehicle/addVehicle.jsp
echo.
echo 📖 如遇问题，请查看"部署说明/故障排除.md"
echo =====================================================
echo.

:: 询问是否打开浏览器
set /p choice="是否现在打开浏览器访问系统？(Y/N): "
if /i "%choice%"=="Y" (
    start http://localhost:8080/vehicle/
)

echo.
echo 按任意键退出...
pause >nul
