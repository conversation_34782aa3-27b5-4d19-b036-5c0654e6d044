<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.sql.*" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>🔧 多端口数据库连接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 20px; }
        .test-item { background: white; margin: 10px 0; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .success { border-left: 5px solid #4CAF50; background-color: #f1f8e9; }
        .error { border-left: 5px solid #f44336; background-color: #ffebee; }
        .info { border-left: 5px solid #2196F3; background-color: #e3f2fd; }
        .config { font-family: monospace; background: #f5f5f5; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .btn { background: #2196F3; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #1976D2; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 多端口数据库连接测试</h1>
            <p>测试不同端口和配置的SQL Server连接</p>
        </div>

        <%
            // 测试配置数组
            String[][] configs = {
                {"127.0.0.1:1434 + SQL认证", "*************************************************************************************************", "vehicle_user", "Vehicle123!"},
                {"127.0.0.1:1433 + SQL认证", "*************************************************************************************************", "vehicle_user", "Vehicle123!"},
                {"localhost:1434 + SQL认证", "*************************************************************************************************", "vehicle_user", "Vehicle123!"},
                {"localhost:1433 + SQL认证", "*************************************************************************************************", "vehicle_user", "Vehicle123!"},
                {"127.0.0.1:1434 + Windows认证", "*************************************************************************************************************************", "", ""},
                {"127.0.0.1:1433 + Windows认证", "*************************************************************************************************************************", "", ""},
                {"计算机名:1433 + SQL认证", "*******************************************************************************************************", "vehicle_user", "Vehicle123!"},
                {"计算机名:1434 + SQL认证", "*******************************************************************************************************", "vehicle_user", "Vehicle123!"}
            };

            for (int i = 0; i < configs.length; i++) {
                String configName = configs[i][0];
                String url = configs[i][1];
                String user = configs[i][2];
                String password = configs[i][3];
                
                out.println("<div class='test-item'>");
                out.println("<h3>📋 测试 " + (i+1) + ": " + configName + "</h3>");
                out.println("<div class='config'>URL: " + url + "<br>");
                if (!user.isEmpty()) {
                    out.println("用户: " + user + "<br>密码: " + password);
                } else {
                    out.println("认证: Windows集成身份验证");
                }
                out.println("</div>");

                Connection conn = null;
                try {
                    Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
                    
                    long startTime = System.currentTimeMillis();
                    if (user.isEmpty()) {
                        conn = DriverManager.getConnection(url);
                    } else {
                        conn = DriverManager.getConnection(url, user, password);
                    }
                    long endTime = System.currentTimeMillis();
                    
                    out.println("<div class='success'>");
                    out.println("<h4>✅ 连接成功！</h4>");
                    out.println("<p>连接时间: " + (endTime - startTime) + "ms</p>");
                    
                    // 测试查询
                    Statement stmt = conn.createStatement();
                    ResultSet rs = stmt.executeQuery("SELECT @@VERSION as version, DB_NAME() as dbname, SYSTEM_USER as user");
                    if (rs.next()) {
                        out.println("<p><strong>数据库信息:</strong></p>");
                        out.println("<p>版本: " + rs.getString("version").substring(0, Math.min(50, rs.getString("version").length())) + "...</p>");
                        out.println("<p>数据库: " + rs.getString("dbname") + "</p>");
                        out.println("<p>用户: " + rs.getString("user") + "</p>");
                    }
                    rs.close();
                    stmt.close();
                    out.println("</div>");
                    
                    // 如果这是第一个成功的连接，显示推荐配置
                    if (i == 0 || request.getParameter("showRecommendation") == null) {
                        out.println("<div class='info'>");
                        out.println("<h4>🎉 推荐使用此配置！</h4>");
                        out.println("<p>请在 DBUtil.java 中使用以下配置：</p>");
                        out.println("<div class='config'>");
                        out.println("private static final String URL = \"" + url + "\";<br>");
                        out.println("private static final String USER = \"" + user + "\";<br>");
                        out.println("private static final String PASSWORD = \"" + password + "\";");
                        out.println("</div>");
                        out.println("</div>");
                    }
                    
                } catch (Exception e) {
                    out.println("<div class='error'>");
                    out.println("<h4>❌ 连接失败</h4>");
                    out.println("<p><strong>错误类型:</strong> " + e.getClass().getSimpleName() + "</p>");
                    out.println("<p><strong>错误信息:</strong> " + e.getMessage() + "</p>");
                    if (e instanceof SQLException) {
                        SQLException sqlEx = (SQLException) e;
                        out.println("<p><strong>SQL状态:</strong> " + sqlEx.getSQLState() + "</p>");
                        out.println("<p><strong>错误代码:</strong> " + sqlEx.getErrorCode() + "</p>");
                    }
                    out.println("</div>");
                } finally {
                    if (conn != null) {
                        try { conn.close(); } catch (Exception e) {}
                    }
                }
                out.println("</div>");
            }
        %>

        <div class="test-item info">
            <h3>🔧 解决建议</h3>
            <ul>
                <li><strong>如果所有连接都失败：</strong>
                    <ul>
                        <li>检查SQL Server服务是否运行</li>
                        <li>在SQL Server Configuration Manager中启用TCP/IP协议</li>
                        <li>检查防火墙设置</li>
                        <li>确保数据库 'vehicle_db' 存在</li>
                    </ul>
                </li>
                <li><strong>如果SQL认证失败：</strong>
                    <ul>
                        <li>确保已创建用户 'vehicle_user'</li>
                        <li>检查SQL Server是否启用了混合身份验证模式</li>
                    </ul>
                </li>
                <li><strong>如果Windows认证失败：</strong>
                    <ul>
                        <li>确保当前Windows用户有数据库访问权限</li>
                        <li>尝试使用SQL Server身份验证</li>
                    </ul>
                </li>
            </ul>
        </div>

        <div style="text-align: center; margin: 20px;">
            <a href="index.jsp" class="btn">返回首页</a>
            <a href="simpleTest.jsp" class="btn">简单测试</a>
            <a href="multiPortTest.jsp" class="btn">刷新测试</a>
        </div>
    </div>
</body>
</html>
