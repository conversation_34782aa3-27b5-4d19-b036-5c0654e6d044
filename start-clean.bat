@echo off
title Vehicle Management System

REM Clean PATH without conda
set "PATH=C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem"

REM Set Java and Maven
set "JAVA_HOME=D:\javaJDK8"
set "MAVEN_HOME=D:\XunLai\apache-maven-3.9.10-bin\apache-maven-3.9.10"
set "PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%"

REM Clear conda variables
set CONDA_DEFAULT_ENV=
set CONDA_EXE=
set CONDA_PREFIX=
set CONDA_PROMPT_MODIFIER=
set CONDA_PYTHON_EXE=
set CONDA_ROOT=
set CONDA_SHLVL=
set PYTHONPATH=

echo ========================================
echo Vehicle Management System Launcher
echo ========================================
echo.

echo Checking Java...
java -version
if errorlevel 1 (
    echo ERROR: Java not found!
    pause
    exit /b 1
)
echo.

echo Checking Maven...
mvn -version
if errorlevel 1 (
    echo ERROR: Maven not found!
    pause
    exit /b 1
)
echo.

echo Changing to project directory...
cd /d "D:\projects\vehicle-system"
if errorlevel 1 (
    echo ERROR: Project directory not found!
    pause
    exit /b 1
)

echo Starting project...
echo Project will be available at http://localhost:8080/vehicle
echo Press Ctrl+C to stop the server
echo.

mvn clean compile tomcat7:run

echo.
echo Project stopped.
pause
