/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-06-20 03:09:22 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;

public final class index_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\r\n");
      out.write("<!DOCTYPE html>\r\n");
      out.write("<html>\r\n");
      out.write("<head>\r\n");
      out.write("    <meta charset=\"UTF-8\">\r\n");
      out.write("    <title>车辆管理系统 - 首页</title>\r\n");
      out.write("    <style>\r\n");
      out.write("        * {\r\n");
      out.write("            margin: 0;\r\n");
      out.write("            padding: 0;\r\n");
      out.write("            box-sizing: border-box;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        body {\r\n");
      out.write("            font-family: 'Microsoft YaHei', Arial, sans-serif;\r\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n");
      out.write("            min-height: 100vh;\r\n");
      out.write("            padding: 20px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .container {\r\n");
      out.write("            max-width: 1000px;\r\n");
      out.write("            margin: 0 auto;\r\n");
      out.write("            background: white;\r\n");
      out.write("            border-radius: 15px;\r\n");
      out.write("            box-shadow: 0 20px 40px rgba(0,0,0,0.1);\r\n");
      out.write("            overflow: hidden;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .header {\r\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n");
      out.write("            color: white;\r\n");
      out.write("            padding: 50px 30px;\r\n");
      out.write("            text-align: center;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .header h1 {\r\n");
      out.write("            font-size: 3em;\r\n");
      out.write("            margin-bottom: 15px;\r\n");
      out.write("            text-shadow: 0 2px 4px rgba(0,0,0,0.3);\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .header p {\r\n");
      out.write("            font-size: 1.2em;\r\n");
      out.write("            opacity: 0.9;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .content {\r\n");
      out.write("            padding: 50px 30px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .features {\r\n");
      out.write("            display: grid;\r\n");
      out.write("            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\r\n");
      out.write("            gap: 30px;\r\n");
      out.write("            margin-bottom: 40px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .feature-card {\r\n");
      out.write("            background: #f8f9fa;\r\n");
      out.write("            padding: 30px;\r\n");
      out.write("            border-radius: 15px;\r\n");
      out.write("            text-align: center;\r\n");
      out.write("            transition: transform 0.3s ease, box-shadow 0.3s ease;\r\n");
      out.write("            border: 1px solid #e9ecef;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .feature-card:hover {\r\n");
      out.write("            transform: translateY(-5px);\r\n");
      out.write("            box-shadow: 0 15px 30px rgba(0,0,0,0.1);\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .feature-icon {\r\n");
      out.write("            font-size: 3em;\r\n");
      out.write("            margin-bottom: 20px;\r\n");
      out.write("            display: block;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .feature-title {\r\n");
      out.write("            font-size: 1.3em;\r\n");
      out.write("            font-weight: 600;\r\n");
      out.write("            margin-bottom: 15px;\r\n");
      out.write("            color: #495057;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .feature-desc {\r\n");
      out.write("            color: #6c757d;\r\n");
      out.write("            line-height: 1.6;\r\n");
      out.write("            margin-bottom: 20px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .btn {\r\n");
      out.write("            padding: 12px 24px;\r\n");
      out.write("            border: none;\r\n");
      out.write("            border-radius: 8px;\r\n");
      out.write("            cursor: pointer;\r\n");
      out.write("            font-size: 14px;\r\n");
      out.write("            font-weight: 600;\r\n");
      out.write("            text-decoration: none;\r\n");
      out.write("            display: inline-block;\r\n");
      out.write("            transition: all 0.3s ease;\r\n");
      out.write("            margin: 5px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .btn-primary {\r\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n");
      out.write("            color: white;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .btn-primary:hover {\r\n");
      out.write("            transform: translateY(-2px);\r\n");
      out.write("            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .btn-success {\r\n");
      out.write("            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);\r\n");
      out.write("            color: white;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .btn-info {\r\n");
      out.write("            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);\r\n");
      out.write("            color: white;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .quick-actions {\r\n");
      out.write("            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\r\n");
      out.write("            padding: 30px;\r\n");
      out.write("            border-radius: 15px;\r\n");
      out.write("            text-align: center;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .quick-actions h2 {\r\n");
      out.write("            color: #495057;\r\n");
      out.write("            margin-bottom: 20px;\r\n");
      out.write("            font-size: 1.8em;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .action-buttons {\r\n");
      out.write("            display: flex;\r\n");
      out.write("            justify-content: center;\r\n");
      out.write("            gap: 15px;\r\n");
      out.write("            flex-wrap: wrap;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .system-info {\r\n");
      out.write("            background: #e3f2fd;\r\n");
      out.write("            padding: 20px;\r\n");
      out.write("            border-radius: 10px;\r\n");
      out.write("            margin-top: 30px;\r\n");
      out.write("            border-left: 5px solid #2196f3;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .system-info h3 {\r\n");
      out.write("            color: #1976d2;\r\n");
      out.write("            margin-bottom: 10px;\r\n");
      out.write("        }\r\n");
      out.write("\r\n");
      out.write("        .system-info p {\r\n");
      out.write("            color: #424242;\r\n");
      out.write("            line-height: 1.6;\r\n");
      out.write("        }\r\n");
      out.write("    </style>\r\n");
      out.write("</head>\r\n");
      out.write("<body>\r\n");
      out.write("    <div class=\"container\">\r\n");
      out.write("        <div class=\"header\">\r\n");
      out.write("            <h1>🚗 车辆管理系统</h1>\r\n");
      out.write("            <p>专业的车辆信息管理平台 - 让车辆管理更简单高效</p>\r\n");
      out.write("        </div>\r\n");
      out.write("\r\n");
      out.write("        <div class=\"content\">\r\n");
      out.write("            <div class=\"features\">\r\n");
      out.write("                <div class=\"feature-card\">\r\n");
      out.write("                    <span class=\"feature-icon\">📋</span>\r\n");
      out.write("                    <div class=\"feature-title\">车辆列表管理</div>\r\n");
      out.write("                    <div class=\"feature-desc\">查看所有车辆信息，支持搜索和筛选功能，实时统计车辆状态</div>\r\n");
      out.write("                    <a href=\"vehicles.jsp\" class=\"btn btn-primary\">查看车辆列表</a>\r\n");
      out.write("                </div>\r\n");
      out.write("\r\n");
      out.write("                <div class=\"feature-card\">\r\n");
      out.write("                    <span class=\"feature-icon\">➕</span>\r\n");
      out.write("                    <div class=\"feature-title\">添加新车辆</div>\r\n");
      out.write("                    <div class=\"feature-desc\">快速录入新车辆信息，包括车牌号、品牌型号、购买日期等详细信息</div>\r\n");
      out.write("                    <a href=\"addVehicle.jsp\" class=\"btn btn-success\">添加新车辆</a>\r\n");
      out.write("                </div>\r\n");
      out.write("\r\n");
      out.write("                <div class=\"feature-card\">\r\n");
      out.write("                    <span class=\"feature-icon\">🔧</span>\r\n");
      out.write("                    <div class=\"feature-title\">系统测试</div>\r\n");
      out.write("                    <div class=\"feature-desc\">验证数据库连接状态，查看系统运行情况和基础功能测试</div>\r\n");
      out.write("                    <a href=\"test\" class=\"btn btn-info\">系统测试</a>\r\n");
      out.write("                </div>\r\n");
      out.write("            </div>\r\n");
      out.write("\r\n");
      out.write("            <div class=\"quick-actions\">\r\n");
      out.write("                <h2>🚀 快速操作</h2>\r\n");
      out.write("                <div class=\"action-buttons\">\r\n");
      out.write("                    <a href=\"vehicles.jsp\" class=\"btn btn-primary\">📊 车辆总览</a>\r\n");
      out.write("                    <a href=\"addVehicle.jsp\" class=\"btn btn-success\">🆕 快速添加</a>\r\n");
      out.write("                    <a href=\"test\" class=\"btn btn-info\">🔍 系统检查</a>\r\n");
      out.write("                </div>\r\n");
      out.write("            </div>\r\n");
      out.write("\r\n");
      out.write("            <div class=\"system-info\">\r\n");
      out.write("                <h3>💡 系统特色</h3>\r\n");
      out.write("                <p>\r\n");
      out.write("                    <strong>• 完整的CRUD操作：</strong> 支持车辆信息的增加、查询、修改、删除<br>\r\n");
      out.write("                    <strong>• 智能搜索功能：</strong> 可按车牌号、品牌、型号快速搜索<br>\r\n");
      out.write("                    <strong>• 状态管理：</strong> 实时跟踪车辆可用、已租出、维修中等状态<br>\r\n");
      out.write("                    <strong>• 数据统计：</strong> 自动统计各类车辆数量，提供数据概览<br>\r\n");
      out.write("                    <strong>• 响应式设计：</strong> 支持各种设备访问，界面美观易用\r\n");
      out.write("                </p>\r\n");
      out.write("            </div>\r\n");
      out.write("        </div>\r\n");
      out.write("    </div>\r\n");
      out.write("\r\n");
      out.write("    <script>\r\n");
      out.write("        // 页面加载动画\r\n");
      out.write("        document.addEventListener('DOMContentLoaded', function() {\r\n");
      out.write("            const cards = document.querySelectorAll('.feature-card');\r\n");
      out.write("            cards.forEach((card, index) => {\r\n");
      out.write("                setTimeout(() => {\r\n");
      out.write("                    card.style.opacity = '0';\r\n");
      out.write("                    card.style.transform = 'translateY(30px)';\r\n");
      out.write("                    card.style.transition = 'all 0.6s ease';\r\n");
      out.write("\r\n");
      out.write("                    setTimeout(() => {\r\n");
      out.write("                        card.style.opacity = '1';\r\n");
      out.write("                        card.style.transform = 'translateY(0)';\r\n");
      out.write("                    }, 100);\r\n");
      out.write("                }, index * 200);\r\n");
      out.write("            });\r\n");
      out.write("        });\r\n");
      out.write("    </script>\r\n");
      out.write("</body>\r\n");
      out.write("</html>");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
