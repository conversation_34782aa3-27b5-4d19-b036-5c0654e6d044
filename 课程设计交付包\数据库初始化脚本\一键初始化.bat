@echo off
chcp 65001 >nul
echo =====================================================
echo 车辆管理系统 - 数据库一键初始化脚本
echo 课程设计项目
echo =====================================================
echo.

echo 正在初始化数据库，请稍候...
echo.

echo [1/3] 创建数据库和用户...
sqlcmd -S localhost -E -i "01_创建数据库和用户.sql"
if %errorlevel% neq 0 (
    echo 错误：数据库和用户创建失败！
    pause
    exit /b 1
)
echo ✓ 数据库和用户创建成功
echo.

echo [2/3] 创建表结构...
sqlcmd -S localhost -E -i "02_创建表结构.sql"
if %errorlevel% neq 0 (
    echo 错误：表结构创建失败！
    pause
    exit /b 1
)
echo ✓ 表结构创建成功
echo.

echo [3/3] 插入测试数据...
sqlcmd -S localhost -E -i "03_插入测试数据.sql"
if %errorlevel% neq 0 (
    echo 错误：测试数据插入失败！
    pause
    exit /b 1
)
echo ✓ 测试数据插入成功
echo.

echo =====================================================
echo 🎉 数据库初始化完成！
echo.
echo 数据库信息：
echo   - 数据库名：vehicle_db
echo   - 用户名：vehicle_user
echo   - 密码：Vehicle123!
echo   - 测试数据：15条车辆记录
echo.
echo 现在可以启动车辆管理系统了！
echo =====================================================
pause
