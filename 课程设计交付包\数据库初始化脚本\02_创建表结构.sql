-- =====================================================
-- 车辆管理系统 - 表结构创建脚本
-- 课程设计项目
-- 创建时间: 2025-06-17
-- =====================================================

-- 确保使用正确的数据库
USE vehicle_db;
GO

-- 删除现有表（如果存在）
IF OBJECT_ID('dbo.vehicle', 'U') IS NOT NULL
BEGIN
    DROP TABLE dbo.vehicle;
    PRINT '已删除现有车辆表';
END
GO

-- 创建车辆信息表
CREATE TABLE vehicle (
    id INT IDENTITY(1,1) PRIMARY KEY,                    -- 车辆ID（主键，自增）
    license_plate NVARCHAR(20) NOT NULL UNIQUE,         -- 车牌号（唯一）
    brand NVARCHAR(50) NOT NULL,                        -- 品牌
    model NVARCHAR(50) NOT NULL,                        -- 型号
    purchase_date DATE,                                  -- 购买日期
    mileage INT DEFAULT 0,                              -- 里程数（公里）
    status NVARCHAR(20) DEFAULT '可用',                 -- 车辆状态
    created_time DATETIME DEFAULT GETDATE(),            -- 创建时间
    updated_time DATETIME DEFAULT GETDATE()             -- 更新时间
);
GO

-- 添加表注释
EXEC sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'车辆信息表', 
    @level0type = N'SCHEMA', @level0name = N'dbo', 
    @level1type = N'TABLE', @level1name = N'vehicle';
GO

-- 添加字段注释
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'车辆ID（主键）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'vehicle', @level2type = N'COLUMN', @level2name = N'id';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'车牌号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'vehicle', @level2type = N'COLUMN', @level2name = N'license_plate';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'车辆品牌', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'vehicle', @level2type = N'COLUMN', @level2name = N'brand';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'车辆型号', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'vehicle', @level2type = N'COLUMN', @level2name = N'model';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'购买日期', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'vehicle', @level2type = N'COLUMN', @level2name = N'purchase_date';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'里程数（公里）', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'vehicle', @level2type = N'COLUMN', @level2name = N'mileage';
EXEC sp_addextendedproperty @name = N'MS_Description', @value = N'车辆状态', @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'vehicle', @level2type = N'COLUMN', @level2name = N'status';
GO

-- 创建索引
CREATE INDEX IX_vehicle_license_plate ON vehicle(license_plate);
CREATE INDEX IX_vehicle_brand ON vehicle(brand);
CREATE INDEX IX_vehicle_status ON vehicle(status);
GO

PRINT '=== 车辆表创建完成 ===';
