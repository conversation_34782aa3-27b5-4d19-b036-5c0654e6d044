package com.vehicle.util;

import java.sql.*;

public class DBUtil {
    // 🔧 数据库连接配置 - 请根据您的实际情况选择一种方案

    // 🔧 数据库连接配置 - 请根据您的实际情况选择一种方案

    // 🔧 数据库连接配置 - 根据您的SQL Server配置调整

    // 🔧 数据库连接配置 - 使用SQL Server身份验证

    // 🔧 数据库连接配置 - 根据端口检查结果调整

    // 📋 方案1: 使用localhost默认端口 + SQL Server身份验证
    private static final String URL = "********************************************************************************************";
    private static final String USER = "vehicle_user"; // SQL Server用户名
    private static final String PASSWORD = "Vehicle123!"; // SQL Server密码

    // 📋 方案2: 端口1433（备用）
    // private static final String URL = "*************************************************************************************************";
    // private static final String USER = "vehicle_user";
    // private static final String PASSWORD = "Vehicle123!";

    // 📋 方案3: 使用localhost而不是127.0.0.1
    // private static final String URL = "*************************************************************************************************";
    // private static final String USER = "vehicle_user";
    // private static final String PASSWORD = "Vehicle123!";

    // 📋 方案4: Windows身份验证 + 端口1434
    // private static final String URL = "*************************************************************************************************************************";
    // private static final String USER = "";
    // private static final String PASSWORD = "";

    static {
        try {
            Class.forName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        } catch (ClassNotFoundException e) {
            System.err.println("数据库驱动加载失败: " + e.getMessage());
            throw new ExceptionInInitializerError(e);
        }
    }

    public static Connection getConnection() throws SQLException {
        System.out.println("连接SQL Server数据库: " + URL);

        // 如果使用Windows身份验证（USER和PASSWORD为空）
        if (USER.isEmpty() && PASSWORD.isEmpty()) {
            return DriverManager.getConnection(URL);
        } else {
            // 使用SQL Server身份验证
            return DriverManager.getConnection(URL, USER, PASSWORD);
        }
    }
}