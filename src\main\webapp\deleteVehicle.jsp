<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.sql.*" %>
<%@ page import="com.vehicle.util.DBUtil" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>删除车辆 - 车辆管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px;
        }
        
        .vehicle-info {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 5px solid #ff416c;
        }
        
        .vehicle-info h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .vehicle-detail {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .detail-label {
            font-weight: 600;
            color: #6c757d;
        }
        
        .detail-value {
            color: #495057;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .warning-box h4 {
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 65, 108, 0.4);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .form-actions {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
            text-align: center;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
            text-align: center;
        }
        
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            min-width: 80px;
            display: inline-block;
        }
        
        .status-available {
            background: #d4edda;
            color: #155724;
        }
        
        .status-rented {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-maintenance {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗑️ 删除车辆</h1>
            <p>确认删除车辆信息</p>
        </div>
        
        <div class="content">
            <%
                String message = "";
                String messageType = "";
                String vehicleId = request.getParameter("id");
                String confirm = request.getParameter("confirm");
                
                // 车辆信息变量
                String licensePlate = "";
                String brand = "";
                String model = "";
                String purchaseDate = "";
                int mileage = 0;
                String status = "";
                boolean vehicleFound = false;
                
                if (vehicleId == null || vehicleId.trim().isEmpty()) {
                    response.sendRedirect("vehicles.jsp");
                    return;
                }
                
                // 处理删除确认
                if ("yes".equals(confirm)) {
                    try {
                        Connection conn = DBUtil.getConnection();
                        String sql = "DELETE FROM vehicle WHERE id = ?";
                        PreparedStatement pstmt = conn.prepareStatement(sql);
                        pstmt.setInt(1, Integer.parseInt(vehicleId));
                        
                        int result = pstmt.executeUpdate();
                        
                        if (result > 0) {
                            message = "✅ 车辆删除成功！";
                            messageType = "success";
                        } else {
                            message = "❌ 删除失败，车辆可能已被删除。";
                            messageType = "error";
                        }
                        
                        pstmt.close();
                        conn.close();
                        
                    } catch (SQLException e) {
                        message = "❌ 数据库错误：" + e.getMessage();
                        messageType = "error";
                    } catch (NumberFormatException e) {
                        message = "❌ 无效的车辆ID。";
                        messageType = "error";
                    }
                } else {
                    // 获取车辆信息用于确认
                    try {
                        Connection conn = DBUtil.getConnection();
                        String sql = "SELECT * FROM vehicle WHERE id = ?";
                        PreparedStatement pstmt = conn.prepareStatement(sql);
                        pstmt.setInt(1, Integer.parseInt(vehicleId));
                        ResultSet rs = pstmt.executeQuery();
                        
                        if (rs.next()) {
                            licensePlate = rs.getString("license_plate");
                            brand = rs.getString("brand");
                            model = rs.getString("model");
                            Date dbPurchaseDate = rs.getDate("purchase_date");
                            purchaseDate = dbPurchaseDate != null ? dbPurchaseDate.toString() : "未知";
                            mileage = rs.getInt("mileage");
                            status = rs.getString("status");
                            vehicleFound = true;
                        } else {
                            message = "❌ 未找到指定的车辆信息。";
                            messageType = "error";
                        }
                        
                        rs.close();
                        pstmt.close();
                        conn.close();
                        
                    } catch (SQLException e) {
                        message = "❌ 数据库连接错误：" + e.getMessage();
                        messageType = "error";
                    } catch (NumberFormatException e) {
                        message = "❌ 无效的车辆ID。";
                        messageType = "error";
                    }
                }
            %>
            
            <% if (!message.isEmpty()) { %>
                <div class="<%= messageType.equals("success") ? "success-message" : "error-message" %>">
                    <%= message %>
                    <% if (messageType.equals("success")) { %>
                        <br><br>
                        <a href="vehicles.jsp" class="btn btn-secondary">📋 返回车辆列表</a>
                        <a href="index.jsp" class="btn btn-secondary">🏠 返回首页</a>
                    <% } %>
                </div>
            <% } %>
            
            <% if (vehicleFound && !"yes".equals(confirm)) { %>
                <div class="vehicle-info">
                    <h3>🚗 即将删除的车辆信息</h3>
                    
                    <div class="vehicle-detail">
                        <div class="detail-item">
                            <span class="detail-label">车辆ID:</span>
                            <span class="detail-value"><%= vehicleId %></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">车牌号:</span>
                            <span class="detail-value"><strong><%= licensePlate %></strong></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">品牌:</span>
                            <span class="detail-value"><%= brand %></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">型号:</span>
                            <span class="detail-value"><%= model %></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">购买日期:</span>
                            <span class="detail-value"><%= purchaseDate %></span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">里程数:</span>
                            <span class="detail-value"><%= String.format("%,d", mileage) %> 公里</span>
                        </div>
                    </div>
                    
                    <div class="detail-item">
                        <span class="detail-label">当前状态:</span>
                        <span class="detail-value">
                            <%
                                String statusClass = "";
                                String statusText = "";
                                if ("available".equals(status)) {
                                    statusClass = "status-available";
                                    statusText = "可用";
                                } else if ("rented".equals(status)) {
                                    statusClass = "status-rented";
                                    statusText = "已租出";
                                } else if ("maintenance".equals(status)) {
                                    statusClass = "status-maintenance";
                                    statusText = "维修中";
                                } else {
                                    statusClass = "status-available";
                                    statusText = status;
                                }
                            %>
                            <span class="status <%= statusClass %>"><%= statusText %></span>
                        </span>
                    </div>
                </div>
                
                <div class="warning-box">
                    <h4>⚠️ 警告</h4>
                    <p>您即将删除车辆 <strong><%= licensePlate %></strong> 的所有信息。</p>
                    <p>此操作不可撤销，请确认是否继续？</p>
                </div>
                
                <div class="form-actions">
                    <a href="deleteVehicle.jsp?id=<%= vehicleId %>&confirm=yes" 
                       class="btn btn-danger"
                       onclick="return confirm('确定要删除车辆 <%= licensePlate %> 吗？此操作不可撤销！')">
                        🗑️ 确认删除
                    </a>
                    <a href="vehicles.jsp" class="btn btn-secondary">❌ 取消删除</a>
                    <a href="editVehicle.jsp?id=<%= vehicleId %>" class="btn btn-secondary">✏️ 编辑车辆</a>
                </div>
            <% } %>
            
            <% if (!vehicleFound && message.isEmpty()) { %>
                <div class="error-message">
                    <h4>❌ 车辆不存在</h4>
                    <p>未找到指定的车辆信息，可能已被删除。</p>
                    <br>
                    <a href="vehicles.jsp" class="btn btn-secondary">📋 返回车辆列表</a>
                    <a href="index.jsp" class="btn btn-secondary">🏠 返回首页</a>
                </div>
            <% } %>
        </div>
    </div>
</body>
</html>
