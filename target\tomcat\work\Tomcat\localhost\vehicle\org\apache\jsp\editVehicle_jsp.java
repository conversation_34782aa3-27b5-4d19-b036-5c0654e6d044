/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-06-25 06:59:20 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import java.sql.*;
import com.vehicle.util.DBUtil;

public final class editVehicle_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html>\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <title>编辑车辆 - 车辆管理系统</title>\n");
      out.write("    <style>\n");
      out.write("        * {\n");
      out.write("            margin: 0;\n");
      out.write("            padding: 0;\n");
      out.write("            box-sizing: border-box;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        body {\n");
      out.write("            font-family: 'Microsoft YaHei', Arial, sans-serif;\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("            min-height: 100vh;\n");
      out.write("            padding: 20px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .container {\n");
      out.write("            max-width: 800px;\n");
      out.write("            margin: 0 auto;\n");
      out.write("            background: white;\n");
      out.write("            border-radius: 15px;\n");
      out.write("            box-shadow: 0 20px 40px rgba(0,0,0,0.1);\n");
      out.write("            overflow: hidden;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .header {\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("            color: white;\n");
      out.write("            padding: 30px;\n");
      out.write("            text-align: center;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .header h1 {\n");
      out.write("            font-size: 2.5em;\n");
      out.write("            margin-bottom: 10px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .header p {\n");
      out.write("            font-size: 1.1em;\n");
      out.write("            opacity: 0.9;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .form-container {\n");
      out.write("            padding: 40px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .form-group {\n");
      out.write("            margin-bottom: 25px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .form-group label {\n");
      out.write("            display: block;\n");
      out.write("            margin-bottom: 8px;\n");
      out.write("            font-weight: 600;\n");
      out.write("            color: #333;\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .form-group input,\n");
      out.write("        .form-group select {\n");
      out.write("            width: 100%;\n");
      out.write("            padding: 12px 15px;\n");
      out.write("            border: 2px solid #e9ecef;\n");
      out.write("            border-radius: 8px;\n");
      out.write("            font-size: 14px;\n");
      out.write("            transition: border-color 0.3s ease;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .form-group input:focus,\n");
      out.write("        .form-group select:focus {\n");
      out.write("            outline: none;\n");
      out.write("            border-color: #667eea;\n");
      out.write("            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .form-row {\n");
      out.write("            display: grid;\n");
      out.write("            grid-template-columns: 1fr 1fr;\n");
      out.write("            gap: 20px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn {\n");
      out.write("            padding: 12px 24px;\n");
      out.write("            border: none;\n");
      out.write("            border-radius: 8px;\n");
      out.write("            cursor: pointer;\n");
      out.write("            font-size: 14px;\n");
      out.write("            font-weight: 600;\n");
      out.write("            text-decoration: none;\n");
      out.write("            display: inline-block;\n");
      out.write("            transition: all 0.3s ease;\n");
      out.write("            margin: 5px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-primary {\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("            color: white;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-primary:hover {\n");
      out.write("            transform: translateY(-2px);\n");
      out.write("            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-secondary {\n");
      out.write("            background: #6c757d;\n");
      out.write("            color: white;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-secondary:hover {\n");
      out.write("            background: #5a6268;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .form-actions {\n");
      out.write("            text-align: center;\n");
      out.write("            margin-top: 30px;\n");
      out.write("            padding-top: 20px;\n");
      out.write("            border-top: 1px solid #e9ecef;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .success-message {\n");
      out.write("            background: #d4edda;\n");
      out.write("            color: #155724;\n");
      out.write("            padding: 15px;\n");
      out.write("            border-radius: 8px;\n");
      out.write("            margin-bottom: 20px;\n");
      out.write("            border: 1px solid #c3e6cb;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .error-message {\n");
      out.write("            background: #f8d7da;\n");
      out.write("            color: #721c24;\n");
      out.write("            padding: 15px;\n");
      out.write("            border-radius: 8px;\n");
      out.write("            margin-bottom: 20px;\n");
      out.write("            border: 1px solid #f5c6cb;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .required {\n");
      out.write("            color: #dc3545;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .form-help {\n");
      out.write("            font-size: 12px;\n");
      out.write("            color: #6c757d;\n");
      out.write("            margin-top: 5px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .vehicle-info {\n");
      out.write("            background: #f8f9fa;\n");
      out.write("            padding: 20px;\n");
      out.write("            border-radius: 8px;\n");
      out.write("            margin-bottom: 30px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .vehicle-info h3 {\n");
      out.write("            color: #495057;\n");
      out.write("            margin-bottom: 10px;\n");
      out.write("        }\n");
      out.write("    </style>\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <div class=\"header\">\n");
      out.write("            <h1>✏️ 编辑车辆信息</h1>\n");
      out.write("            <p>修改车辆详细信息</p>\n");
      out.write("        </div>\n");
      out.write("        \n");
      out.write("        <div class=\"form-container\">\n");
      out.write("            ");

                String message = "";
                String messageType = "";
                String vehicleId = request.getParameter("id");
                
                // 车辆信息变量
                String licensePlate = "";
                String brand = "";
                String model = "";
                String purchaseDate = "";
                String mileage = "";
                String status = "";
                
                if (vehicleId == null || vehicleId.trim().isEmpty()) {
                    response.sendRedirect("vehicles.jsp");
                    return;
                }
                
                // 处理表单提交
                if ("POST".equals(request.getMethod())) {
                    licensePlate = request.getParameter("licensePlate");
                    brand = request.getParameter("brand");
                    model = request.getParameter("model");
                    purchaseDate = request.getParameter("purchaseDate");
                    String mileageStr = request.getParameter("mileage");
                    status = request.getParameter("status");
                    
                    // 验证必填字段
                    if (licensePlate != null && !licensePlate.trim().isEmpty() &&
                        brand != null && !brand.trim().isEmpty() &&
                        model != null && !model.trim().isEmpty()) {
                        
                        try {
                            int mileageInt = 0;
                            if (mileageStr != null && !mileageStr.trim().isEmpty()) {
                                mileageInt = Integer.parseInt(mileageStr);
                            }
                            
                            Connection conn = DBUtil.getConnection();
                            String sql = "UPDATE vehicle SET license_plate=?, brand=?, model=?, purchase_date=?, mileage=?, status=? WHERE id=?";
                            PreparedStatement pstmt = conn.prepareStatement(sql);
                            
                            pstmt.setString(1, licensePlate.trim());
                            pstmt.setString(2, brand.trim());
                            pstmt.setString(3, model.trim());
                            
                            if (purchaseDate != null && !purchaseDate.trim().isEmpty()) {
                                pstmt.setDate(4, Date.valueOf(purchaseDate));
                            } else {
                                pstmt.setNull(4, Types.DATE);
                            }
                            
                            pstmt.setInt(5, mileageInt);
                            pstmt.setString(6, status != null ? status : "available");
                            pstmt.setInt(7, Integer.parseInt(vehicleId));
                            
                            int result = pstmt.executeUpdate();
                            
                            if (result > 0) {
                                message = "✅ 车辆信息更新成功！";
                                messageType = "success";
                            } else {
                                message = "❌ 更新失败，请重试。";
                                messageType = "error";
                            }
                            
                            pstmt.close();
                            conn.close();
                            
                        } catch (SQLException e) {
                            if (e.getMessage().contains("PRIMARY KEY") || e.getMessage().contains("UNIQUE")) {
                                message = "❌ 车牌号已存在，请使用其他车牌号。";
                            } else {
                                message = "❌ 数据库错误：" + e.getMessage();
                            }
                            messageType = "error";
                        } catch (NumberFormatException e) {
                            message = "❌ 里程数必须是有效的数字。";
                            messageType = "error";
                        } catch (IllegalArgumentException e) {
                            message = "❌ 购买日期格式不正确，请使用 YYYY-MM-DD 格式。";
                            messageType = "error";
                        }
                    } else {
                        message = "❌ 请填写所有必填字段（车牌号、品牌、型号）。";
                        messageType = "error";
                    }
                }
                
                // 获取车辆信息
                try {
                    Connection conn = DBUtil.getConnection();
                    String sql = "SELECT * FROM vehicle WHERE id = ?";
                    PreparedStatement pstmt = conn.prepareStatement(sql);
                    pstmt.setInt(1, Integer.parseInt(vehicleId));
                    ResultSet rs = pstmt.executeQuery();
                    
                    if (rs.next()) {
                        // 如果不是POST请求，使用数据库中的值
                        if (!"POST".equals(request.getMethod())) {
                            licensePlate = rs.getString("license_plate");
                            brand = rs.getString("brand");
                            model = rs.getString("model");
                            Date dbPurchaseDate = rs.getDate("purchase_date");
                            purchaseDate = dbPurchaseDate != null ? dbPurchaseDate.toString() : "";
                            mileage = String.valueOf(rs.getInt("mileage"));
                            status = rs.getString("status");
                        }
                    } else {
                        message = "❌ 未找到指定的车辆信息。";
                        messageType = "error";
                    }
                    
                    rs.close();
                    pstmt.close();
                    conn.close();
                    
                } catch (SQLException e) {
                    message = "❌ 数据库连接错误：" + e.getMessage();
                    messageType = "error";
                } catch (NumberFormatException e) {
                    message = "❌ 无效的车辆ID。";
                    messageType = "error";
                }
            
      out.write("\n");
      out.write("            \n");
      out.write("            ");
 if (!message.isEmpty()) { 
      out.write("\n");
      out.write("                <div class=\"");
      out.print( messageType.equals("success") ? "success-message" : "error-message" );
      out.write("\">\n");
      out.write("                    ");
      out.print( message );
      out.write("\n");
      out.write("                </div>\n");
      out.write("            ");
 } 
      out.write("\n");
      out.write("            \n");
      out.write("            <div class=\"vehicle-info\">\n");
      out.write("                <h3>🚗 正在编辑车辆 ID: ");
      out.print( vehicleId );
      out.write("</h3>\n");
      out.write("                <p>请修改下方的车辆信息，然后点击\"更新车辆信息\"按钮保存更改。</p>\n");
      out.write("            </div>\n");
      out.write("            \n");
      out.write("            <form method=\"post\" action=\"editVehicle.jsp?id=");
      out.print( vehicleId );
      out.write("\">\n");
      out.write("                <div class=\"form-row\">\n");
      out.write("                    <div class=\"form-group\">\n");
      out.write("                        <label for=\"licensePlate\">车牌号 <span class=\"required\">*</span></label>\n");
      out.write("                        <input type=\"text\" id=\"licensePlate\" name=\"licensePlate\" \n");
      out.write("                               value=\"");
      out.print( licensePlate );
      out.write("\" placeholder=\"例如：京A12345\" required>\n");
      out.write("                        <div class=\"form-help\">请输入完整的车牌号码</div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"form-group\">\n");
      out.write("                        <label for=\"status\">车辆状态</label>\n");
      out.write("                        <select id=\"status\" name=\"status\">\n");
      out.write("                            <option value=\"available\" ");
      out.print( "available".equals(status) ? "selected" : "" );
      out.write(">可用</option>\n");
      out.write("                            <option value=\"rented\" ");
      out.print( "rented".equals(status) ? "selected" : "" );
      out.write(">已租出</option>\n");
      out.write("                            <option value=\"maintenance\" ");
      out.print( "maintenance".equals(status) ? "selected" : "" );
      out.write(">维修中</option>\n");
      out.write("                        </select>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"form-row\">\n");
      out.write("                    <div class=\"form-group\">\n");
      out.write("                        <label for=\"brand\">品牌 <span class=\"required\">*</span></label>\n");
      out.write("                        <input type=\"text\" id=\"brand\" name=\"brand\" \n");
      out.write("                               value=\"");
      out.print( brand );
      out.write("\" placeholder=\"例如：丰田\" required>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"form-group\">\n");
      out.write("                        <label for=\"model\">型号 <span class=\"required\">*</span></label>\n");
      out.write("                        <input type=\"text\" id=\"model\" name=\"model\" \n");
      out.write("                               value=\"");
      out.print( model );
      out.write("\" placeholder=\"例如：凯美瑞\" required>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"form-row\">\n");
      out.write("                    <div class=\"form-group\">\n");
      out.write("                        <label for=\"purchaseDate\">购买日期</label>\n");
      out.write("                        <input type=\"date\" id=\"purchaseDate\" name=\"purchaseDate\" value=\"");
      out.print( purchaseDate );
      out.write("\">\n");
      out.write("                        <div class=\"form-help\">选择车辆购买日期</div>\n");
      out.write("                    </div>\n");
      out.write("                    \n");
      out.write("                    <div class=\"form-group\">\n");
      out.write("                        <label for=\"mileage\">里程数（公里）</label>\n");
      out.write("                        <input type=\"number\" id=\"mileage\" name=\"mileage\" min=\"0\" \n");
      out.write("                               value=\"");
      out.print( mileage );
      out.write("\" placeholder=\"例如：15000\">\n");
      out.write("                        <div class=\"form-help\">当前车辆的总里程数</div>\n");
      out.write("                    </div>\n");
      out.write("                </div>\n");
      out.write("                \n");
      out.write("                <div class=\"form-actions\">\n");
      out.write("                    <button type=\"submit\" class=\"btn btn-primary\">💾 更新车辆信息</button>\n");
      out.write("                    <a href=\"vehicles.jsp\" class=\"btn btn-secondary\">📋 返回车辆列表</a>\n");
      out.write("                    <a href=\"index.jsp\" class=\"btn btn-secondary\">🏠 返回首页</a>\n");
      out.write("                </div>\n");
      out.write("            </form>\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("    \n");
      out.write("    <script>\n");
      out.write("        // 表单验证\n");
      out.write("        document.querySelector('form').addEventListener('submit', function(e) {\n");
      out.write("            const licensePlate = document.getElementById('licensePlate').value.trim();\n");
      out.write("            const brand = document.getElementById('brand').value.trim();\n");
      out.write("            const model = document.getElementById('model').value.trim();\n");
      out.write("            \n");
      out.write("            if (!licensePlate || !brand || !model) {\n");
      out.write("                e.preventDefault();\n");
      out.write("                alert('请填写所有必填字段（车牌号、品牌、型号）');\n");
      out.write("                return false;\n");
      out.write("            }\n");
      out.write("        });\n");
      out.write("        \n");
      out.write("        // 自动格式化车牌号\n");
      out.write("        document.getElementById('licensePlate').addEventListener('input', function(e) {\n");
      out.write("            this.value = this.value.toUpperCase();\n");
      out.write("        });\n");
      out.write("    </script>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
