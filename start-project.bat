@echo off
echo 正在启动车辆管理系统...
echo.

REM 设置Java和Maven路径
set JAVA_HOME=D:\javaJDK8
set MAVEN_HOME=D:\XunLai\apache-maven-3.9.10-bin\apache-maven-3.9.10
set PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%

REM 显示版本信息
echo 检查Java版本:
java -version
echo.

echo 检查Maven版本:
mvn -version
echo.

REM 切换到项目目录
cd /d "D:\projects\vehicle-system"

echo 正在编译和启动项目...
echo 项目将在 http://localhost:8080/vehicle 启动
echo 按 Ctrl+C 停止服务器
echo.

REM 启动项目
mvn clean compile tomcat7:run

pause
