# 🔧 数据库连接配置说明

## 当前问题
Windows身份验证失败，无法连接到SQL Server数据库。

## 解决方案

### 方案1：修复Windows身份验证（推荐）

1. **检查SQL Server配置管理器**：
   - 打开"SQL Server Configuration Manager"
   - 展开"SQL Server Network Configuration"
   - 点击"Protocols for MSSQLSERVER"（或您的实例名）
   - 确保"TCP/IP"协议已启用
   - 右键点击"TCP/IP" → "Properties"
   - 在"IP Addresses"选项卡中，找到"IPAll"部分
   - 设置"TCP Port"为1433

2. **重启SQL Server服务**：
   - 在"SQL Server Services"中重启SQL Server服务

3. **检查Windows防火墙**：
   - 确保端口1433已开放

### 方案2：使用SQL Server身份验证

1. **在SSMS中执行**：
```sql
-- 启用混合身份验证模式
USE master;
EXEC xp_instance_regwrite N'HKEY_LOCAL_MACHINE', 
     N'Software\Microsoft\MSSQLServer\MSSQLServer', 
     N'LoginMode', REG_DWORD, 2;

-- 创建新用户
CREATE LOGIN vehicle_user WITH PASSWORD = 'Vehicle123!';
USE vehicle_db;
CREATE USER vehicle_user FOR LOGIN vehicle_user;
ALTER ROLE db_owner ADD MEMBER vehicle_user;
```

2. **重启SQL Server服务**

3. **修改Java配置**：
   在DBUtil.java中使用SQL Server身份验证的配置

### 方案3：使用不同的连接字符串

尝试以下连接字符串（在DBUtil.java中）：

```java
// 选项1：localhost
"*************************************************************************************************************************"

// 选项2：127.0.0.1
"*************************************************************************************************************************"

// 选项3：计算机名
"*******************************************************************************************************************************"

// 选项4：SQL Server身份验证
"*************************************************************************************************"
// 配合 USER = "vehicle_user", PASSWORD = "Vehicle123!"
```

## 测试步骤

1. 访问：http://localhost:8080/vehicle/simpleTest.jsp
2. 查看详细错误信息
3. 根据错误信息选择合适的解决方案
4. 修改配置后重新编译和启动项目

## 常见错误及解决方法

- **连接超时**：检查SQL Server服务是否运行，TCP/IP是否启用
- **身份验证失败**：尝试SQL Server身份验证
- **数据库不存在**：确认数据库名称，重新创建数据库
- **端口问题**：检查SQL Server使用的端口号
