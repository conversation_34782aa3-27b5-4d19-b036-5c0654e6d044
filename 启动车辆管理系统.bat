@echo off
title 车辆管理系统

REM 完全绕过conda，使用干净的环境
set "PATH=C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem"

REM 设置Java和Maven的完整路径
set "JAVA_HOME=D:\javaJDK8"
set "MAVEN_HOME=D:\XunLai\apache-maven-3.9.10-bin\apache-maven-3.9.10"
set "PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%"

REM 清除所有conda相关环境变量
set CONDA_DEFAULT_ENV=
set CONDA_EXE=
set CONDA_PREFIX=
set CONDA_PROMPT_MODIFIER=
set CONDA_PYTHON_EXE=
set CONDA_ROOT=
set CONDA_SHLVL=
set PYTHONPATH=
set PYTHONIOENCODING=

echo ========================================
echo 车辆管理系统启动器
echo ========================================
echo.

echo 正在检查Java环境...
"%JAVA_HOME%\bin\java.exe" -version
if errorlevel 1 (
    echo 错误：Java未正确安装！
    pause
    exit /b 1
)
echo.

echo 正在检查Maven环境...
"%MAVEN_HOME%\bin\mvn.cmd" -version
if errorlevel 1 (
    echo 错误：Maven未正确安装！
    pause
    exit /b 1
)
echo.

echo 切换到项目目录...
cd /d "D:\projects\vehicle-system"
if errorlevel 1 (
    echo 错误：无法找到项目目录！
    pause
    exit /b 1
)

echo 开始编译和启动项目...
echo 项目将在以下地址启动：
echo   http://localhost:8080/vehicle
echo.
echo 按 Ctrl+C 停止服务器
echo ========================================
echo.

"%MAVEN_HOME%\bin\mvn.cmd" clean compile tomcat7:run

echo.
echo 项目已停止运行。
pause
