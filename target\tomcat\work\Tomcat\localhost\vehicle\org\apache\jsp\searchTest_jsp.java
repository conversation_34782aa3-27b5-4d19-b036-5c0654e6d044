/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-06-20 03:19:20 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import com.vehicle.dao.VehicleDAO;
import com.vehicle.dao.VehicleDaoImpl;
import com.vehicle.model.Vehicle;
import java.util.List;

public final class searchTest_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html>\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <title>搜索功能测试</title>\n");
      out.write("    <style>\n");
      out.write("        body { font-family: Arial, sans-serif; margin: 20px; }\n");
      out.write("        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }\n");
      out.write("        .success { background-color: #d4edda; border-color: #c3e6cb; }\n");
      out.write("        .error { background-color: #f8d7da; border-color: #f5c6cb; }\n");
      out.write("    </style>\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <h1>🔍 搜索功能测试页面</h1>\n");
      out.write("    \n");
      out.write("    ");

        VehicleDAO dao = new VehicleDaoImpl();
        
        // 测试1: 搜索"丰田"
        try {
            List<Vehicle> result1 = dao.searchVehicles("丰田");
    
      out.write("\n");
      out.write("            <div class=\"test-result success\">\n");
      out.write("                <h3>✅ 测试1: 搜索\"丰田\"</h3>\n");
      out.write("                <p>找到 ");
      out.print( result1.size() );
      out.write(" 条结果:</p>\n");
      out.write("                ");
 for (Vehicle v : result1) { 
      out.write("\n");
      out.write("                    <p>- ");
      out.print( v.getLicensePlate() );
      out.write(' ');
      out.write('|');
      out.write(' ');
      out.print( v.getBrand() );
      out.write(' ');
      out.write('|');
      out.write(' ');
      out.print( v.getModel() );
      out.write("</p>\n");
      out.write("                ");
 } 
      out.write("\n");
      out.write("            </div>\n");
      out.write("    ");

        } catch (Exception e) {
    
      out.write("\n");
      out.write("            <div class=\"test-result error\">\n");
      out.write("                <h3>❌ 测试1失败: ");
      out.print( e.getMessage() );
      out.write("</h3>\n");
      out.write("            </div>\n");
      out.write("    ");

        }
        
        // 测试2: 搜索"京A"
        try {
            List<Vehicle> result2 = dao.searchVehicles("京A");
    
      out.write("\n");
      out.write("            <div class=\"test-result success\">\n");
      out.write("                <h3>✅ 测试2: 搜索\"京A\"</h3>\n");
      out.write("                <p>找到 ");
      out.print( result2.size() );
      out.write(" 条结果:</p>\n");
      out.write("                ");
 for (Vehicle v : result2) { 
      out.write("\n");
      out.write("                    <p>- ");
      out.print( v.getLicensePlate() );
      out.write(' ');
      out.write('|');
      out.write(' ');
      out.print( v.getBrand() );
      out.write(' ');
      out.write('|');
      out.write(' ');
      out.print( v.getModel() );
      out.write("</p>\n");
      out.write("                ");
 } 
      out.write("\n");
      out.write("            </div>\n");
      out.write("    ");

        } catch (Exception e) {
    
      out.write("\n");
      out.write("            <div class=\"test-result error\">\n");
      out.write("                <h3>❌ 测试2失败: ");
      out.print( e.getMessage() );
      out.write("</h3>\n");
      out.write("            </div>\n");
      out.write("    ");

        }
        
        // 测试3: 搜索"维修"
        try {
            List<Vehicle> result3 = dao.searchVehicles("维修");
    
      out.write("\n");
      out.write("            <div class=\"test-result success\">\n");
      out.write("                <h3>✅ 测试3: 搜索\"维修\"</h3>\n");
      out.write("                <p>找到 ");
      out.print( result3.size() );
      out.write(" 条结果:</p>\n");
      out.write("                ");
 for (Vehicle v : result3) { 
      out.write("\n");
      out.write("                    <p>- ");
      out.print( v.getLicensePlate() );
      out.write(' ');
      out.write('|');
      out.write(' ');
      out.print( v.getBrand() );
      out.write(' ');
      out.write('|');
      out.write(' ');
      out.print( v.getModel() );
      out.write(' ');
      out.write('|');
      out.write(' ');
      out.print( v.getStatus() );
      out.write("</p>\n");
      out.write("                ");
 } 
      out.write("\n");
      out.write("            </div>\n");
      out.write("    ");

        } catch (Exception e) {
    
      out.write("\n");
      out.write("            <div class=\"test-result error\">\n");
      out.write("                <h3>❌ 测试3失败: ");
      out.print( e.getMessage() );
      out.write("</h3>\n");
      out.write("            </div>\n");
      out.write("    ");

        }
        
        // 测试4: 搜索空字符串
        try {
            List<Vehicle> result4 = dao.searchVehicles("");
    
      out.write("\n");
      out.write("            <div class=\"test-result success\">\n");
      out.write("                <h3>✅ 测试4: 搜索空字符串（应返回所有车辆）</h3>\n");
      out.write("                <p>找到 ");
      out.print( result4.size() );
      out.write(" 条结果</p>\n");
      out.write("            </div>\n");
      out.write("    ");

        } catch (Exception e) {
    
      out.write("\n");
      out.write("            <div class=\"test-result error\">\n");
      out.write("                <h3>❌ 测试4失败: ");
      out.print( e.getMessage() );
      out.write("</h3>\n");
      out.write("            </div>\n");
      out.write("    ");

        }
        
        // 测试5: 搜索不存在的内容
        try {
            List<Vehicle> result5 = dao.searchVehicles("不存在的车辆");
    
      out.write("\n");
      out.write("            <div class=\"test-result success\">\n");
      out.write("                <h3>✅ 测试5: 搜索不存在的内容</h3>\n");
      out.write("                <p>找到 ");
      out.print( result5.size() );
      out.write(" 条结果（应该为0）</p>\n");
      out.write("            </div>\n");
      out.write("    ");

        } catch (Exception e) {
    
      out.write("\n");
      out.write("            <div class=\"test-result error\">\n");
      out.write("                <h3>❌ 测试5失败: ");
      out.print( e.getMessage() );
      out.write("</h3>\n");
      out.write("            </div>\n");
      out.write("    ");

        }
    
      out.write("\n");
      out.write("    \n");
      out.write("    <hr>\n");
      out.write("    <p><a href=\"vehicle\">返回车辆列表</a> | <a href=\"index.jsp\">返回首页</a></p>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
