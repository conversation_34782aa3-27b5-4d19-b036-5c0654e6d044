/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-06-20 03:26:52 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import java.sql.*;
import java.util.Date;
import com.vehicle.util.DBUtil;
import com.vehicle.model.Vehicle;
import java.util.List;

public final class vehicles_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private org.apache.jasper.runtime.TagHandlerPool _005fjspx_005ftagPool_005fc_005fif_0026_005ftest;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest = org.apache.jasper.runtime.TagHandlerPool.getTagHandlerPool(getServletConfig());
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.release();
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html>\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <title>车辆管理系统 - 车辆列表</title>\n");
      out.write("    <style>\n");
      out.write("        * {\n");
      out.write("            margin: 0;\n");
      out.write("            padding: 0;\n");
      out.write("            box-sizing: border-box;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        body {\n");
      out.write("            font-family: 'Microsoft YaHei', Arial, sans-serif;\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("            min-height: 100vh;\n");
      out.write("            padding: 20px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .container {\n");
      out.write("            max-width: 1200px;\n");
      out.write("            margin: 0 auto;\n");
      out.write("            background: white;\n");
      out.write("            border-radius: 15px;\n");
      out.write("            box-shadow: 0 20px 40px rgba(0,0,0,0.1);\n");
      out.write("            overflow: hidden;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .header {\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("            color: white;\n");
      out.write("            padding: 30px;\n");
      out.write("            text-align: center;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .header h1 {\n");
      out.write("            font-size: 2.5em;\n");
      out.write("            margin-bottom: 10px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .header p {\n");
      out.write("            font-size: 1.1em;\n");
      out.write("            opacity: 0.9;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .toolbar {\n");
      out.write("            padding: 20px 30px;\n");
      out.write("            background: #f8f9fa;\n");
      out.write("            border-bottom: 1px solid #e9ecef;\n");
      out.write("            display: flex;\n");
      out.write("            justify-content: space-between;\n");
      out.write("            align-items: center;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn {\n");
      out.write("            padding: 12px 24px;\n");
      out.write("            border: none;\n");
      out.write("            border-radius: 8px;\n");
      out.write("            cursor: pointer;\n");
      out.write("            font-size: 14px;\n");
      out.write("            font-weight: 600;\n");
      out.write("            text-decoration: none;\n");
      out.write("            display: inline-block;\n");
      out.write("            transition: all 0.3s ease;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-primary {\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("            color: white;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-primary:hover {\n");
      out.write("            transform: translateY(-2px);\n");
      out.write("            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-success {\n");
      out.write("            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);\n");
      out.write("            color: white;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-warning {\n");
      out.write("            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n");
      out.write("            color: white;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-danger {\n");
      out.write("            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);\n");
      out.write("            color: white;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-sm {\n");
      out.write("            padding: 8px 16px;\n");
      out.write("            font-size: 12px;\n");
      out.write("            margin: 0 2px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .search-box {\n");
      out.write("            display: flex;\n");
      out.write("            align-items: center;\n");
      out.write("            gap: 10px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .search-box input {\n");
      out.write("            padding: 10px 15px;\n");
      out.write("            border: 2px solid #e9ecef;\n");
      out.write("            border-radius: 8px;\n");
      out.write("            font-size: 14px;\n");
      out.write("            width: 250px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .search-box input:focus {\n");
      out.write("            outline: none;\n");
      out.write("            border-color: #667eea;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .content {\n");
      out.write("            padding: 30px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .vehicle-table {\n");
      out.write("            width: 100%;\n");
      out.write("            border-collapse: collapse;\n");
      out.write("            margin-top: 20px;\n");
      out.write("            background: white;\n");
      out.write("            border-radius: 10px;\n");
      out.write("            overflow: hidden;\n");
      out.write("            box-shadow: 0 5px 15px rgba(0,0,0,0.08);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .vehicle-table th {\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("            color: white;\n");
      out.write("            padding: 15px;\n");
      out.write("            text-align: left;\n");
      out.write("            font-weight: 600;\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .vehicle-table td {\n");
      out.write("            padding: 15px;\n");
      out.write("            border-bottom: 1px solid #f1f3f4;\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .vehicle-table tr:hover {\n");
      out.write("            background: #f8f9fa;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .status {\n");
      out.write("            padding: 6px 12px;\n");
      out.write("            border-radius: 20px;\n");
      out.write("            font-size: 12px;\n");
      out.write("            font-weight: 600;\n");
      out.write("            text-align: center;\n");
      out.write("            min-width: 80px;\n");
      out.write("            display: inline-block;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .status-available {\n");
      out.write("            background: #d4edda;\n");
      out.write("            color: #155724;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .status-rented {\n");
      out.write("            background: #fff3cd;\n");
      out.write("            color: #856404;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .status-maintenance {\n");
      out.write("            background: #f8d7da;\n");
      out.write("            color: #721c24;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .actions {\n");
      out.write("            display: flex;\n");
      out.write("            gap: 5px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .stats {\n");
      out.write("            display: grid;\n");
      out.write("            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n");
      out.write("            gap: 20px;\n");
      out.write("            margin-bottom: 30px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .stat-card {\n");
      out.write("            background: white;\n");
      out.write("            padding: 20px;\n");
      out.write("            border-radius: 10px;\n");
      out.write("            box-shadow: 0 5px 15px rgba(0,0,0,0.08);\n");
      out.write("            text-align: center;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .stat-number {\n");
      out.write("            font-size: 2em;\n");
      out.write("            font-weight: bold;\n");
      out.write("            color: #667eea;\n");
      out.write("            margin-bottom: 5px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .stat-label {\n");
      out.write("            color: #6c757d;\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .empty-state {\n");
      out.write("            text-align: center;\n");
      out.write("            padding: 60px 20px;\n");
      out.write("            color: #6c757d;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .empty-state i {\n");
      out.write("            font-size: 4em;\n");
      out.write("            margin-bottom: 20px;\n");
      out.write("            opacity: 0.3;\n");
      out.write("        }\n");
      out.write("    </style>\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <div class=\"header\">\n");
      out.write("            <h1>🚗 车辆管理系统</h1>\n");
      out.write("            <p>专业的车辆信息管理平台</p>\n");
      out.write("        </div>\n");
      out.write("        \n");
      out.write("        <div class=\"toolbar\">\n");
      out.write("            <div>\n");
      out.write("                <a href=\"addVehicle.jsp\" class=\"btn btn-primary\">\n");
      out.write("                    ➕ 添加新车辆\n");
      out.write("                </a>\n");
      out.write("                <a href=\"index.jsp\" class=\"btn btn-success\">\n");
      out.write("                    🏠 返回首页\n");
      out.write("                </a>\n");
      out.write("            </div>\n");
      out.write("            <div class=\"search-box\">\n");
      out.write("                <form method=\"get\" action=\"vehicle\" style=\"display: flex; align-items: center; gap: 10px;\">\n");
      out.write("                    <input type=\"text\" name=\"search\" placeholder=\"搜索车牌号、品牌或型号...\"\n");
      out.write("                           value=\"");
      out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.search}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
      out.write("\" style=\"padding: 10px 15px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 14px; width: 250px;\">\n");
      out.write("                    <button type=\"submit\" class=\"btn btn-primary\">🔍 搜索</button>\n");
      out.write("                    <a href=\"vehicle\" class=\"btn btn-success\">🔄 显示全部</a>\n");
      out.write("                </form>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("        \n");
      out.write("        <div class=\"content\">\n");
      out.write("            <!-- 显示搜索结果信息 -->\n");
      out.write("            ");
      if (_jspx_meth_c_005fif_005f0(_jspx_page_context))
        return;
      out.write("\n");
      out.write("\n");
      out.write("            <!-- 显示错误信息 -->\n");
      out.write("            ");
      if (_jspx_meth_c_005fif_005f1(_jspx_page_context))
        return;
      out.write("\n");
      out.write("\n");
      out.write("            ");

                // 首先尝试从Servlet获取数据
                List<Vehicle> vehicles = (List<Vehicle>) request.getAttribute("vehicles");

                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;

                int totalVehicles = 0;
                int availableVehicles = 0;
                int rentedVehicles = 0;
                int maintenanceVehicles = 0;

                try {
                    // 如果Servlet没有提供数据，则直接查询数据库
                    if (vehicles == null) {
                        conn = DBUtil.getConnection();
                        stmt = conn.createStatement();

                        // 统计数据
                        rs = stmt.executeQuery("SELECT status, COUNT(*) as count FROM vehicle GROUP BY status");
                        while (rs.next()) {
                            String status = rs.getString("status");
                            int count = rs.getInt("count");
                            totalVehicles += count;

                            if ("可用".equals(status)) {
                                availableVehicles = count;
                            } else if ("已租出".equals(status)) {
                                rentedVehicles = count;
                            } else if ("维修中".equals(status)) {
                                maintenanceVehicles = count;
                            }
                        }
                    } else {
                        // 使用Servlet提供的数据进行统计
                        totalVehicles = vehicles.size();
                        for (Vehicle v : vehicles) {
                            String status = v.getStatus();
                            if ("可用".equals(status)) {
                                availableVehicles++;
                            } else if ("已租出".equals(status)) {
                                rentedVehicles++;
                            } else if ("维修中".equals(status)) {
                                maintenanceVehicles++;
                            }
                        }
                    }
            
      out.write("\n");
      out.write("            \n");
      out.write("            <!-- 统计卡片 -->\n");
      out.write("            <div class=\"stats\">\n");
      out.write("                <div class=\"stat-card\">\n");
      out.write("                    <div class=\"stat-number\">");
      out.print( totalVehicles );
      out.write("</div>\n");
      out.write("                    <div class=\"stat-label\">总车辆数</div>\n");
      out.write("                </div>\n");
      out.write("                <div class=\"stat-card\">\n");
      out.write("                    <div class=\"stat-number\">");
      out.print( availableVehicles );
      out.write("</div>\n");
      out.write("                    <div class=\"stat-label\">可用车辆</div>\n");
      out.write("                </div>\n");
      out.write("                <div class=\"stat-card\">\n");
      out.write("                    <div class=\"stat-number\">");
      out.print( rentedVehicles );
      out.write("</div>\n");
      out.write("                    <div class=\"stat-label\">已租出</div>\n");
      out.write("                </div>\n");
      out.write("                <div class=\"stat-card\">\n");
      out.write("                    <div class=\"stat-number\">");
      out.print( maintenanceVehicles );
      out.write("</div>\n");
      out.write("                    <div class=\"stat-label\">维修中</div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            ");

                    // 显示车辆列表
                    boolean hasVehicles = false;

                    if (vehicles != null && !vehicles.isEmpty()) {
                        hasVehicles = true;
                    } else if (vehicles == null) {
                        // 如果Servlet没有提供数据，直接查询数据库
                        if (rs != null) rs.close();
                        rs = stmt.executeQuery("SELECT * FROM vehicle ORDER BY id DESC");
                        hasVehicles = rs.isBeforeFirst();
                    }

                    if (!hasVehicles) {
            
      out.write("\n");
      out.write("                        <div class=\"empty-state\">\n");
      out.write("                            <div style=\"font-size: 4em; margin-bottom: 20px;\">🚗</div>\n");
      out.write("                            <h3>暂无车辆数据</h3>\n");
      out.write("                            <p>点击上方\"添加新车辆\"按钮开始添加车辆信息</p>\n");
      out.write("                            ");
      if (_jspx_meth_c_005fif_005f2(_jspx_page_context))
        return;
      out.write("\n");
      out.write("                        </div>\n");
      out.write("            ");

                    } else {
            
      out.write("\n");
      out.write("                        <table class=\"vehicle-table\" id=\"vehicleTable\">\n");
      out.write("                            <thead>\n");
      out.write("                                <tr>\n");
      out.write("                                    <th>ID</th>\n");
      out.write("                                    <th>车牌号</th>\n");
      out.write("                                    <th>品牌</th>\n");
      out.write("                                    <th>型号</th>\n");
      out.write("                                    <th>购买日期</th>\n");
      out.write("                                    <th>里程数</th>\n");
      out.write("                                    <th>状态</th>\n");
      out.write("                                    <th>操作</th>\n");
      out.write("                                </tr>\n");
      out.write("                            </thead>\n");
      out.write("                            <tbody>\n");
      out.write("            ");

                        if (vehicles != null) {
                            // 使用Servlet提供的数据
                            for (Vehicle vehicle : vehicles) {
                                int id = vehicle.getId();
                                String licensePlate = vehicle.getLicensePlate();
                                String brand = vehicle.getBrand();
                                String model = vehicle.getModel();
                                java.util.Date purchaseDate = vehicle.getPurchaseDate();
                                int mileage = vehicle.getMileage();
                                String status = vehicle.getStatus();

                                String statusClass = "";
                                String statusText = status;
                                if ("可用".equals(status)) {
                                    statusClass = "status-available";
                                } else if ("已租出".equals(status)) {
                                    statusClass = "status-rented";
                                } else if ("维修中".equals(status)) {
                                    statusClass = "status-maintenance";
                                } else {
                                    statusClass = "status-available";
                                }
            
      out.write("\n");
      out.write("                                <tr>\n");
      out.write("                                    <td>");
      out.print( id );
      out.write("</td>\n");
      out.write("                                    <td><strong>");
      out.print( licensePlate );
      out.write("</strong></td>\n");
      out.write("                                    <td>");
      out.print( brand );
      out.write("</td>\n");
      out.write("                                    <td>");
      out.print( model );
      out.write("</td>\n");
      out.write("                                    <td>");
      out.print( purchaseDate != null ? purchaseDate.toString() : "未知" );
      out.write("</td>\n");
      out.write("                                    <td>");
      out.print( String.format("%,d", mileage) );
      out.write(" 公里</td>\n");
      out.write("                                    <td><span class=\"status ");
      out.print( statusClass );
      out.write('"');
      out.write('>');
      out.print( statusText );
      out.write("</span></td>\n");
      out.write("                                    <td>\n");
      out.write("                                        <div class=\"actions\">\n");
      out.write("                                            <a href=\"editVehicle.jsp?id=");
      out.print( id );
      out.write("\" class=\"btn btn-warning btn-sm\">✏️ 编辑</a>\n");
      out.write("                                            <a href=\"deleteVehicle.jsp?id=");
      out.print( id );
      out.write("\" class=\"btn btn-danger btn-sm\"\n");
      out.write("                                               onclick=\"return confirm('确定要删除车辆 ");
      out.print( licensePlate );
      out.write(" 吗？')\">🗑️ 删除</a>\n");
      out.write("                                        </div>\n");
      out.write("                                    </td>\n");
      out.write("                                </tr>\n");
      out.write("            ");

                            }
                        } else {
                            // 使用数据库查询结果
                            while (rs.next()) {
                                int id = rs.getInt("id");
                                String licensePlate = rs.getString("license_plate");
                                String brand = rs.getString("brand");
                                String model = rs.getString("model");
                                Date purchaseDate = rs.getDate("purchase_date");
                                int mileage = rs.getInt("mileage");
                                String status = rs.getString("status");

                                String statusClass = "";
                                String statusText = status;
                                if ("可用".equals(status)) {
                                    statusClass = "status-available";
                                } else if ("已租出".equals(status)) {
                                    statusClass = "status-rented";
                                } else if ("维修中".equals(status)) {
                                    statusClass = "status-maintenance";
                                } else {
                                    statusClass = "status-available";
                                }
            
      out.write("\n");
      out.write("                                <tr>\n");
      out.write("                                    <td>");
      out.print( id );
      out.write("</td>\n");
      out.write("                                    <td><strong>");
      out.print( licensePlate );
      out.write("</strong></td>\n");
      out.write("                                    <td>");
      out.print( brand );
      out.write("</td>\n");
      out.write("                                    <td>");
      out.print( model );
      out.write("</td>\n");
      out.write("                                    <td>");
      out.print( purchaseDate != null ? purchaseDate.toString() : "未知" );
      out.write("</td>\n");
      out.write("                                    <td>");
      out.print( String.format("%,d", mileage) );
      out.write(" 公里</td>\n");
      out.write("                                    <td><span class=\"status ");
      out.print( statusClass );
      out.write('"');
      out.write('>');
      out.print( statusText );
      out.write("</span></td>\n");
      out.write("                                    <td>\n");
      out.write("                                        <div class=\"actions\">\n");
      out.write("                                            <a href=\"editVehicle.jsp?id=");
      out.print( id );
      out.write("\" class=\"btn btn-warning btn-sm\">✏️ 编辑</a>\n");
      out.write("                                            <a href=\"deleteVehicle.jsp?id=");
      out.print( id );
      out.write("\" class=\"btn btn-danger btn-sm\"\n");
      out.write("                                               onclick=\"return confirm('确定要删除车辆 ");
      out.print( licensePlate );
      out.write(" 吗？')\">🗑️ 删除</a>\n");
      out.write("                                        </div>\n");
      out.write("                                    </td>\n");
      out.write("                                </tr>\n");
      out.write("            ");

                            }
                        }
            
      out.write("\n");
      out.write("                            </tbody>\n");
      out.write("                        </table>\n");
      out.write("            ");

                    }
                } catch (SQLException e) {
                    out.println("<div style='color: red; padding: 20px; background: #f8d7da; border-radius: 8px;'>");
                    out.println("<h3>❌ 数据库连接错误</h3>");
                    out.println("<p>错误信息: " + e.getMessage() + "</p>");
                    out.println("<p>请检查数据库连接配置和服务状态。</p>");
                    out.println("</div>");
                } finally {
                    if (rs != null) try { rs.close(); } catch (SQLException e) {}
                    if (stmt != null) try { stmt.close(); } catch (SQLException e) {}
                    if (conn != null) try { conn.close(); } catch (SQLException e) {}
                }
            
      out.write("\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("\n");
      out.write("    <script>\n");
      out.write("        // 页面加载完成后的动画效果\n");
      out.write("        document.addEventListener('DOMContentLoaded', function() {\n");
      out.write("            const cards = document.querySelectorAll('.stat-card');\n");
      out.write("            cards.forEach((card, index) => {\n");
      out.write("                setTimeout(() => {\n");
      out.write("                    card.style.opacity = '0';\n");
      out.write("                    card.style.transform = 'translateY(20px)';\n");
      out.write("                    card.style.transition = 'all 0.5s ease';\n");
      out.write("\n");
      out.write("                    setTimeout(() => {\n");
      out.write("                        card.style.opacity = '1';\n");
      out.write("                        card.style.transform = 'translateY(0)';\n");
      out.write("                    }, 100);\n");
      out.write("                }, index * 100);\n");
      out.write("            });\n");
      out.write("        });\n");
      out.write("\n");
      out.write("        // 搜索表单回车提交\n");
      out.write("        document.querySelector('input[name=\"search\"]').addEventListener('keypress', function(e) {\n");
      out.write("            if (e.key === 'Enter') {\n");
      out.write("                this.form.submit();\n");
      out.write("            }\n");
      out.write("        });\n");
      out.write("    </script>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }

  private boolean _jspx_meth_c_005fif_005f0(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f0 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    _jspx_th_c_005fif_005f0.setPageContext(_jspx_page_context);
    _jspx_th_c_005fif_005f0.setParent(null);
    // /vehicles.jsp(256,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_c_005fif_005f0.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty message}", java.lang.Boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false)).booleanValue());
    int _jspx_eval_c_005fif_005f0 = _jspx_th_c_005fif_005f0.doStartTag();
    if (_jspx_eval_c_005fif_005f0 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
      do {
        out.write("\n");
        out.write("                <div style=\"background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #17a2b8;\">\n");
        out.write("                    <strong>📊 ");
        out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${message}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
        out.write("</strong>\n");
        out.write("                </div>\n");
        out.write("            ");
        int evalDoAfterBody = _jspx_th_c_005fif_005f0.doAfterBody();
        if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
          break;
      } while (true);
    }
    if (_jspx_th_c_005fif_005f0.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
      return true;
    }
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f0);
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f1(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f1 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    _jspx_th_c_005fif_005f1.setPageContext(_jspx_page_context);
    _jspx_th_c_005fif_005f1.setParent(null);
    // /vehicles.jsp(263,12) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_c_005fif_005f1.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty error}", java.lang.Boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false)).booleanValue());
    int _jspx_eval_c_005fif_005f1 = _jspx_th_c_005fif_005f1.doStartTag();
    if (_jspx_eval_c_005fif_005f1 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
      do {
        out.write("\n");
        out.write("                <div style=\"background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #dc3545;\">\n");
        out.write("                    <strong>❌ ");
        out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${error}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
        out.write("</strong>\n");
        out.write("                </div>\n");
        out.write("            ");
        int evalDoAfterBody = _jspx_th_c_005fif_005f1.doAfterBody();
        if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
          break;
      } while (true);
    }
    if (_jspx_th_c_005fif_005f1.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
      return true;
    }
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f1);
    return false;
  }

  private boolean _jspx_meth_c_005fif_005f2(javax.servlet.jsp.PageContext _jspx_page_context)
          throws java.lang.Throwable {
    javax.servlet.jsp.PageContext pageContext = _jspx_page_context;
    javax.servlet.jsp.JspWriter out = _jspx_page_context.getOut();
    //  c:if
    org.apache.taglibs.standard.tag.rt.core.IfTag _jspx_th_c_005fif_005f2 = (org.apache.taglibs.standard.tag.rt.core.IfTag) _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.get(org.apache.taglibs.standard.tag.rt.core.IfTag.class);
    _jspx_th_c_005fif_005f2.setPageContext(_jspx_page_context);
    _jspx_th_c_005fif_005f2.setParent(null);
    // /vehicles.jsp(358,28) name = test type = boolean reqTime = true required = true fragment = false deferredValue = false expectedTypeName = null deferredMethod = false methodSignature = null
    _jspx_th_c_005fif_005f2.setTest(((java.lang.Boolean) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${not empty param.search}", java.lang.Boolean.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false)).booleanValue());
    int _jspx_eval_c_005fif_005f2 = _jspx_th_c_005fif_005f2.doStartTag();
    if (_jspx_eval_c_005fif_005f2 != javax.servlet.jsp.tagext.Tag.SKIP_BODY) {
      do {
        out.write("\n");
        out.write("                                <p style=\"color: #6c757d;\">搜索关键词 \"<strong>");
        out.write((java.lang.String) org.apache.jasper.runtime.PageContextImpl.proprietaryEvaluate("${param.search}", java.lang.String.class, (javax.servlet.jsp.PageContext)_jspx_page_context, null, false));
        out.write("</strong>\" 没有找到匹配的车辆</p>\n");
        out.write("                                <a href=\"vehicle\" class=\"btn btn-primary\" style=\"margin-top: 15px;\">显示所有车辆</a>\n");
        out.write("                            ");
        int evalDoAfterBody = _jspx_th_c_005fif_005f2.doAfterBody();
        if (evalDoAfterBody != javax.servlet.jsp.tagext.BodyTag.EVAL_BODY_AGAIN)
          break;
      } while (true);
    }
    if (_jspx_th_c_005fif_005f2.doEndTag() == javax.servlet.jsp.tagext.Tag.SKIP_PAGE) {
      _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
      return true;
    }
    _005fjspx_005ftagPool_005fc_005fif_0026_005ftest.reuse(_jspx_th_c_005fif_005f2);
    return false;
  }
}
