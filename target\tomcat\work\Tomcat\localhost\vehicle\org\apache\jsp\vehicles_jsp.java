/*
 * Generated by the Jasper component of Apache Tomcat
 * Version: Apache Tomcat/7.0.47
 * Generated at: 2025-06-20 03:09:29 UTC
 * Note: The last modified time of this file was set to
 *       the last modified time of the source file after
 *       generation to assist with modification tracking.
 */
package org.apache.jsp;

import javax.servlet.*;
import javax.servlet.http.*;
import javax.servlet.jsp.*;
import java.sql.*;
import com.vehicle.util.DBUtil;

public final class vehicles_jsp extends org.apache.jasper.runtime.HttpJspBase
    implements org.apache.jasper.runtime.JspSourceDependent {

  private static final javax.servlet.jsp.JspFactory _jspxFactory =
          javax.servlet.jsp.JspFactory.getDefaultFactory();

  private static java.util.Map<java.lang.String,java.lang.Long> _jspx_dependants;

  private javax.el.ExpressionFactory _el_expressionfactory;
  private org.apache.tomcat.InstanceManager _jsp_instancemanager;

  public java.util.Map<java.lang.String,java.lang.Long> getDependants() {
    return _jspx_dependants;
  }

  public void _jspInit() {
    _el_expressionfactory = _jspxFactory.getJspApplicationContext(getServletConfig().getServletContext()).getExpressionFactory();
    _jsp_instancemanager = org.apache.jasper.runtime.InstanceManagerFactory.getInstanceManager(getServletConfig());
  }

  public void _jspDestroy() {
  }

  public void _jspService(final javax.servlet.http.HttpServletRequest request, final javax.servlet.http.HttpServletResponse response)
        throws java.io.IOException, javax.servlet.ServletException {

    final javax.servlet.jsp.PageContext pageContext;
    javax.servlet.http.HttpSession session = null;
    final javax.servlet.ServletContext application;
    final javax.servlet.ServletConfig config;
    javax.servlet.jsp.JspWriter out = null;
    final java.lang.Object page = this;
    javax.servlet.jsp.JspWriter _jspx_out = null;
    javax.servlet.jsp.PageContext _jspx_page_context = null;


    try {
      response.setContentType("text/html; charset=UTF-8");
      pageContext = _jspxFactory.getPageContext(this, request, response,
      			null, true, 8192, true);
      _jspx_page_context = pageContext;
      application = pageContext.getServletContext();
      config = pageContext.getServletConfig();
      session = pageContext.getSession();
      out = pageContext.getOut();
      _jspx_out = out;

      out.write("\n");
      out.write("\n");
      out.write("\n");
      out.write("<!DOCTYPE html>\n");
      out.write("<html>\n");
      out.write("<head>\n");
      out.write("    <meta charset=\"UTF-8\">\n");
      out.write("    <title>车辆管理系统 - 车辆列表</title>\n");
      out.write("    <style>\n");
      out.write("        * {\n");
      out.write("            margin: 0;\n");
      out.write("            padding: 0;\n");
      out.write("            box-sizing: border-box;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        body {\n");
      out.write("            font-family: 'Microsoft YaHei', Arial, sans-serif;\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("            min-height: 100vh;\n");
      out.write("            padding: 20px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .container {\n");
      out.write("            max-width: 1200px;\n");
      out.write("            margin: 0 auto;\n");
      out.write("            background: white;\n");
      out.write("            border-radius: 15px;\n");
      out.write("            box-shadow: 0 20px 40px rgba(0,0,0,0.1);\n");
      out.write("            overflow: hidden;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .header {\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("            color: white;\n");
      out.write("            padding: 30px;\n");
      out.write("            text-align: center;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .header h1 {\n");
      out.write("            font-size: 2.5em;\n");
      out.write("            margin-bottom: 10px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .header p {\n");
      out.write("            font-size: 1.1em;\n");
      out.write("            opacity: 0.9;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .toolbar {\n");
      out.write("            padding: 20px 30px;\n");
      out.write("            background: #f8f9fa;\n");
      out.write("            border-bottom: 1px solid #e9ecef;\n");
      out.write("            display: flex;\n");
      out.write("            justify-content: space-between;\n");
      out.write("            align-items: center;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn {\n");
      out.write("            padding: 12px 24px;\n");
      out.write("            border: none;\n");
      out.write("            border-radius: 8px;\n");
      out.write("            cursor: pointer;\n");
      out.write("            font-size: 14px;\n");
      out.write("            font-weight: 600;\n");
      out.write("            text-decoration: none;\n");
      out.write("            display: inline-block;\n");
      out.write("            transition: all 0.3s ease;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-primary {\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("            color: white;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-primary:hover {\n");
      out.write("            transform: translateY(-2px);\n");
      out.write("            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-success {\n");
      out.write("            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);\n");
      out.write("            color: white;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-warning {\n");
      out.write("            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n");
      out.write("            color: white;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-danger {\n");
      out.write("            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);\n");
      out.write("            color: white;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .btn-sm {\n");
      out.write("            padding: 8px 16px;\n");
      out.write("            font-size: 12px;\n");
      out.write("            margin: 0 2px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .search-box {\n");
      out.write("            display: flex;\n");
      out.write("            align-items: center;\n");
      out.write("            gap: 10px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .search-box input {\n");
      out.write("            padding: 10px 15px;\n");
      out.write("            border: 2px solid #e9ecef;\n");
      out.write("            border-radius: 8px;\n");
      out.write("            font-size: 14px;\n");
      out.write("            width: 250px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .search-box input:focus {\n");
      out.write("            outline: none;\n");
      out.write("            border-color: #667eea;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .content {\n");
      out.write("            padding: 30px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .vehicle-table {\n");
      out.write("            width: 100%;\n");
      out.write("            border-collapse: collapse;\n");
      out.write("            margin-top: 20px;\n");
      out.write("            background: white;\n");
      out.write("            border-radius: 10px;\n");
      out.write("            overflow: hidden;\n");
      out.write("            box-shadow: 0 5px 15px rgba(0,0,0,0.08);\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .vehicle-table th {\n");
      out.write("            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
      out.write("            color: white;\n");
      out.write("            padding: 15px;\n");
      out.write("            text-align: left;\n");
      out.write("            font-weight: 600;\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .vehicle-table td {\n");
      out.write("            padding: 15px;\n");
      out.write("            border-bottom: 1px solid #f1f3f4;\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .vehicle-table tr:hover {\n");
      out.write("            background: #f8f9fa;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .status {\n");
      out.write("            padding: 6px 12px;\n");
      out.write("            border-radius: 20px;\n");
      out.write("            font-size: 12px;\n");
      out.write("            font-weight: 600;\n");
      out.write("            text-align: center;\n");
      out.write("            min-width: 80px;\n");
      out.write("            display: inline-block;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .status-available {\n");
      out.write("            background: #d4edda;\n");
      out.write("            color: #155724;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .status-rented {\n");
      out.write("            background: #fff3cd;\n");
      out.write("            color: #856404;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .status-maintenance {\n");
      out.write("            background: #f8d7da;\n");
      out.write("            color: #721c24;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .actions {\n");
      out.write("            display: flex;\n");
      out.write("            gap: 5px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .stats {\n");
      out.write("            display: grid;\n");
      out.write("            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n");
      out.write("            gap: 20px;\n");
      out.write("            margin-bottom: 30px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .stat-card {\n");
      out.write("            background: white;\n");
      out.write("            padding: 20px;\n");
      out.write("            border-radius: 10px;\n");
      out.write("            box-shadow: 0 5px 15px rgba(0,0,0,0.08);\n");
      out.write("            text-align: center;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .stat-number {\n");
      out.write("            font-size: 2em;\n");
      out.write("            font-weight: bold;\n");
      out.write("            color: #667eea;\n");
      out.write("            margin-bottom: 5px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .stat-label {\n");
      out.write("            color: #6c757d;\n");
      out.write("            font-size: 14px;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .empty-state {\n");
      out.write("            text-align: center;\n");
      out.write("            padding: 60px 20px;\n");
      out.write("            color: #6c757d;\n");
      out.write("        }\n");
      out.write("        \n");
      out.write("        .empty-state i {\n");
      out.write("            font-size: 4em;\n");
      out.write("            margin-bottom: 20px;\n");
      out.write("            opacity: 0.3;\n");
      out.write("        }\n");
      out.write("    </style>\n");
      out.write("</head>\n");
      out.write("<body>\n");
      out.write("    <div class=\"container\">\n");
      out.write("        <div class=\"header\">\n");
      out.write("            <h1>🚗 车辆管理系统</h1>\n");
      out.write("            <p>专业的车辆信息管理平台</p>\n");
      out.write("        </div>\n");
      out.write("        \n");
      out.write("        <div class=\"toolbar\">\n");
      out.write("            <div>\n");
      out.write("                <a href=\"addVehicle.jsp\" class=\"btn btn-primary\">\n");
      out.write("                    ➕ 添加新车辆\n");
      out.write("                </a>\n");
      out.write("                <a href=\"index.jsp\" class=\"btn btn-success\">\n");
      out.write("                    🏠 返回首页\n");
      out.write("                </a>\n");
      out.write("            </div>\n");
      out.write("            <div class=\"search-box\">\n");
      out.write("                <input type=\"text\" id=\"searchInput\" placeholder=\"搜索车牌号、品牌或型号...\" onkeyup=\"searchVehicles()\">\n");
      out.write("                <button class=\"btn btn-primary\" onclick=\"searchVehicles()\">🔍 搜索</button>\n");
      out.write("            </div>\n");
      out.write("        </div>\n");
      out.write("        \n");
      out.write("        <div class=\"content\">\n");
      out.write("            ");

                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;
                
                int totalVehicles = 0;
                int availableVehicles = 0;
                int rentedVehicles = 0;
                int maintenanceVehicles = 0;
                
                try {
                    conn = DBUtil.getConnection();
                    stmt = conn.createStatement();
                    
                    // 统计数据
                    rs = stmt.executeQuery("SELECT status, COUNT(*) as count FROM vehicle GROUP BY status");
                    while (rs.next()) {
                        String status = rs.getString("status");
                        int count = rs.getInt("count");
                        totalVehicles += count;
                        
                        if ("available".equals(status)) {
                            availableVehicles = count;
                        } else if ("rented".equals(status)) {
                            rentedVehicles = count;
                        } else if ("maintenance".equals(status)) {
                            maintenanceVehicles = count;
                        }
                    }
            
      out.write("\n");
      out.write("            \n");
      out.write("            <!-- 统计卡片 -->\n");
      out.write("            <div class=\"stats\">\n");
      out.write("                <div class=\"stat-card\">\n");
      out.write("                    <div class=\"stat-number\">");
      out.print( totalVehicles );
      out.write("</div>\n");
      out.write("                    <div class=\"stat-label\">总车辆数</div>\n");
      out.write("                </div>\n");
      out.write("                <div class=\"stat-card\">\n");
      out.write("                    <div class=\"stat-number\">");
      out.print( availableVehicles );
      out.write("</div>\n");
      out.write("                    <div class=\"stat-label\">可用车辆</div>\n");
      out.write("                </div>\n");
      out.write("                <div class=\"stat-card\">\n");
      out.write("                    <div class=\"stat-number\">");
      out.print( rentedVehicles );
      out.write("</div>\n");
      out.write("                    <div class=\"stat-label\">已租出</div>\n");
      out.write("                </div>\n");
      out.write("                <div class=\"stat-card\">\n");
      out.write("                    <div class=\"stat-number\">");
      out.print( maintenanceVehicles );
      out.write("</div>\n");
      out.write("                    <div class=\"stat-label\">维修中</div>\n");
      out.write("                </div>\n");
      out.write("            </div>\n");
      out.write("\n");
      out.write("            ");

                    // 获取车辆列表
                    rs = stmt.executeQuery("SELECT * FROM vehicle ORDER BY id DESC");

                    if (!rs.isBeforeFirst()) {
            
      out.write("\n");
      out.write("                        <div class=\"empty-state\">\n");
      out.write("                            <div style=\"font-size: 4em; margin-bottom: 20px;\">🚗</div>\n");
      out.write("                            <h3>暂无车辆数据</h3>\n");
      out.write("                            <p>点击上方\"添加新车辆\"按钮开始添加车辆信息</p>\n");
      out.write("                        </div>\n");
      out.write("            ");

                    } else {
            
      out.write("\n");
      out.write("                        <table class=\"vehicle-table\" id=\"vehicleTable\">\n");
      out.write("                            <thead>\n");
      out.write("                                <tr>\n");
      out.write("                                    <th>ID</th>\n");
      out.write("                                    <th>车牌号</th>\n");
      out.write("                                    <th>品牌</th>\n");
      out.write("                                    <th>型号</th>\n");
      out.write("                                    <th>购买日期</th>\n");
      out.write("                                    <th>里程数</th>\n");
      out.write("                                    <th>状态</th>\n");
      out.write("                                    <th>操作</th>\n");
      out.write("                                </tr>\n");
      out.write("                            </thead>\n");
      out.write("                            <tbody>\n");
      out.write("            ");

                        while (rs.next()) {
                            int id = rs.getInt("id");
                            String licensePlate = rs.getString("license_plate");
                            String brand = rs.getString("brand");
                            String model = rs.getString("model");
                            Date purchaseDate = rs.getDate("purchase_date");
                            int mileage = rs.getInt("mileage");
                            String status = rs.getString("status");

                            String statusClass = "";
                            String statusText = "";
                            if ("available".equals(status)) {
                                statusClass = "status-available";
                                statusText = "可用";
                            } else if ("rented".equals(status)) {
                                statusClass = "status-rented";
                                statusText = "已租出";
                            } else if ("maintenance".equals(status)) {
                                statusClass = "status-maintenance";
                                statusText = "维修中";
                            } else {
                                statusClass = "status-available";
                                statusText = status;
                            }
            
      out.write("\n");
      out.write("                                <tr>\n");
      out.write("                                    <td>");
      out.print( id );
      out.write("</td>\n");
      out.write("                                    <td><strong>");
      out.print( licensePlate );
      out.write("</strong></td>\n");
      out.write("                                    <td>");
      out.print( brand );
      out.write("</td>\n");
      out.write("                                    <td>");
      out.print( model );
      out.write("</td>\n");
      out.write("                                    <td>");
      out.print( purchaseDate != null ? purchaseDate.toString() : "未知" );
      out.write("</td>\n");
      out.write("                                    <td>");
      out.print( String.format("%,d", mileage) );
      out.write(" 公里</td>\n");
      out.write("                                    <td><span class=\"status ");
      out.print( statusClass );
      out.write('"');
      out.write('>');
      out.print( statusText );
      out.write("</span></td>\n");
      out.write("                                    <td>\n");
      out.write("                                        <div class=\"actions\">\n");
      out.write("                                            <a href=\"editVehicle.jsp?id=");
      out.print( id );
      out.write("\" class=\"btn btn-warning btn-sm\">✏️ 编辑</a>\n");
      out.write("                                            <a href=\"deleteVehicle.jsp?id=");
      out.print( id );
      out.write("\" class=\"btn btn-danger btn-sm\"\n");
      out.write("                                               onclick=\"return confirm('确定要删除车辆 ");
      out.print( licensePlate );
      out.write(" 吗？')\">🗑️ 删除</a>\n");
      out.write("                                        </div>\n");
      out.write("                                    </td>\n");
      out.write("                                </tr>\n");
      out.write("            ");

                        }
            
      out.write("\n");
      out.write("                            </tbody>\n");
      out.write("                        </table>\n");
      out.write("            ");

                    }
                } catch (SQLException e) {
                    out.println("<div style='color: red; padding: 20px; background: #f8d7da; border-radius: 8px;'>");
                    out.println("<h3>❌ 数据库连接错误</h3>");
                    out.println("<p>错误信息: " + e.getMessage() + "</p>");
                    out.println("<p>请检查数据库连接配置和服务状态。</p>");
                    out.println("</div>");
                } finally {
                    if (rs != null) try { rs.close(); } catch (SQLException e) {}
                    if (stmt != null) try { stmt.close(); } catch (SQLException e) {}
                    if (conn != null) try { conn.close(); } catch (SQLException e) {}
                }
            
      out.write("\n");
      out.write("        </div>\n");
      out.write("    </div>\n");
      out.write("\n");
      out.write("    <script>\n");
      out.write("        function searchVehicles() {\n");
      out.write("            const input = document.getElementById('searchInput');\n");
      out.write("            const filter = input.value.toUpperCase();\n");
      out.write("            const table = document.getElementById('vehicleTable');\n");
      out.write("\n");
      out.write("            if (!table) return;\n");
      out.write("\n");
      out.write("            const tr = table.getElementsByTagName('tr');\n");
      out.write("\n");
      out.write("            for (let i = 1; i < tr.length; i++) {\n");
      out.write("                const td = tr[i].getElementsByTagName('td');\n");
      out.write("                let txtValue = '';\n");
      out.write("\n");
      out.write("                // 搜索车牌号、品牌、型号\n");
      out.write("                for (let j = 1; j <= 3; j++) {\n");
      out.write("                    if (td[j]) {\n");
      out.write("                        txtValue += td[j].textContent || td[j].innerText;\n");
      out.write("                    }\n");
      out.write("                }\n");
      out.write("\n");
      out.write("                if (txtValue.toUpperCase().indexOf(filter) > -1) {\n");
      out.write("                    tr[i].style.display = '';\n");
      out.write("                } else {\n");
      out.write("                    tr[i].style.display = 'none';\n");
      out.write("                }\n");
      out.write("            }\n");
      out.write("        }\n");
      out.write("\n");
      out.write("        // 页面加载完成后的动画效果\n");
      out.write("        document.addEventListener('DOMContentLoaded', function() {\n");
      out.write("            const cards = document.querySelectorAll('.stat-card');\n");
      out.write("            cards.forEach((card, index) => {\n");
      out.write("                setTimeout(() => {\n");
      out.write("                    card.style.opacity = '0';\n");
      out.write("                    card.style.transform = 'translateY(20px)';\n");
      out.write("                    card.style.transition = 'all 0.5s ease';\n");
      out.write("\n");
      out.write("                    setTimeout(() => {\n");
      out.write("                        card.style.opacity = '1';\n");
      out.write("                        card.style.transform = 'translateY(0)';\n");
      out.write("                    }, 100);\n");
      out.write("                }, index * 100);\n");
      out.write("            });\n");
      out.write("        });\n");
      out.write("    </script>\n");
      out.write("</body>\n");
      out.write("</html>\n");
    } catch (java.lang.Throwable t) {
      if (!(t instanceof javax.servlet.jsp.SkipPageException)){
        out = _jspx_out;
        if (out != null && out.getBufferSize() != 0)
          try { out.clearBuffer(); } catch (java.io.IOException e) {}
        if (_jspx_page_context != null) _jspx_page_context.handlePageException(t);
        else throw new ServletException(t);
      }
    } finally {
      _jspxFactory.releasePageContext(_jspx_page_context);
    }
  }
}
