# PATH Environment Variable Fix Tool
# Run as Administrator

Write-Host "========================================" -ForegroundColor Green
Write-Host "PATH Environment Variable Fix Tool" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: Administrator privileges required!" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as administrator'" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "Administrator privileges confirmed" -ForegroundColor Green
Write-Host ""

try {
    # Get current PATH
    $currentPath = [Environment]::GetEnvironmentVariable('PATH', 'Machine')
    
    if ($currentPath) {
        # Backup current PATH
        $currentPath | Out-File -FilePath "path_backup.txt" -Encoding UTF8
        Write-Host "Current PATH backed up to path_backup.txt" -ForegroundColor Green
        
        # Show current problematic entries
        Write-Host ""
        Write-Host "Problematic entries found:" -ForegroundColor Yellow
        $currentPath -split ';' | Where-Object { $_ -like '*Jave*IntelliJ*' -or $_ -like '*Vscode*Microsoft VS Code*' } | ForEach-Object {
            Write-Host "  - $_" -ForegroundColor Red
        }
        
        # Clean PATH
        $cleanPath = ($currentPath -split ';' | Where-Object { 
            $_ -notlike '*Jave*IntelliJ*' -and 
            $_ -notlike '*Vscode*Microsoft VS Code*' -and 
            $_ -ne '' 
        }) -join ';'
        
        # Set new PATH
        [Environment]::SetEnvironmentVariable('PATH', $cleanPath, 'Machine')
        
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "PATH cleaned successfully!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        Write-Host "Changes made:" -ForegroundColor Green
        Write-Host "- Removed corrupted PATH entries" -ForegroundColor Green
        Write-Host "- Original PATH backed up to path_backup.txt" -ForegroundColor Green
        Write-Host ""
        Write-Host "IMPORTANT: Please restart your computer for changes to take effect" -ForegroundColor Yellow
        Write-Host "After restart, conda environment issues should be resolved" -ForegroundColor Yellow
        
    } else {
        Write-Host "ERROR: Could not read current PATH" -ForegroundColor Red
    }
    
} catch {
    Write-Host "ERROR: Failed to clean PATH" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please try manual method:" -ForegroundColor Yellow
    Write-Host "1. Press Win + R, type 'sysdm.cpl'" -ForegroundColor Yellow
    Write-Host "2. Click 'Environment Variables'" -ForegroundColor Yellow
    Write-Host "3. Edit 'Path' in System variables" -ForegroundColor Yellow
    Write-Host "4. Remove entries containing corrupted characters" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to exit"
