@echo off

echo ========================================
echo Vehicle Management System
echo ========================================

set "PATH=C:\Windows\System32;C:\Windows;C:\Windows\System32\Wbem"
set "JAVA_HOME=D:\javaJDK8"
set "PATH=%JAVA_HOME%\bin;%PATH%"
set "MAVEN_HOME=D:\XunLai\apache-maven-3.9.10-bin\apache-maven-3.9.10"
set "PATH=%MAVEN_HOME%\bin;%PATH%"

cd /d "D:\projects\vehicle-system"

echo Starting project at http://localhost:8080/vehicle
echo Press Ctrl+C to stop
echo ========================================

mvn compile tomcat7:run

pause
