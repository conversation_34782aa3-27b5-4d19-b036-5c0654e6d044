@echo off
echo 正在修复conda环境问题...
echo.

REM 设置编码
chcp 65001 >nul

REM 临时禁用conda自动激活
set CONDA_AUTO_ACTIVATE_BASE=false
set CONDA_CHANGEPS1=false

REM 清理PATH中的问题字符
set "CLEAN_PATH=D:\javaJDK8\bin;D:\XunLai\apache-maven-3.9.10-bin\apache-maven-3.9.10\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\Program Files\Git\cmd;D:\Miniconda;D:\Miniconda\Scripts;D:\Miniconda\condabin"

REM 使用清理后的PATH
set "PATH=%CLEAN_PATH%"

echo 步骤1: 禁用conda错误报告...
conda config --set report_errors false 2>nul

echo 步骤2: 设置conda配置...
conda config --set changeps1 false 2>nul
conda config --set auto_activate_base false 2>nul

echo 步骤3: 重新初始化conda...
conda init --reverse powershell 2>nul
conda init powershell 2>nul

echo.
echo 修复完成！请重新启动PowerShell或VSCode终端。
echo.
echo 如果问题仍然存在，请使用以下命令启动项目：
echo   cmd /c "set JAVA_HOME=D:\javaJDK8 && set PATH=D:\javaJDK8\bin;D:\XunLai\apache-maven-3.9.10-bin\apache-maven-3.9.10\bin;%%PATH%% && cd /d D:\projects\vehicle-system && mvn clean compile tomcat7:run"
echo.

pause
