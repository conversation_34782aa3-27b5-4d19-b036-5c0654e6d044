<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="com.vehicle.dao.VehicleDAO" %>
<%@ page import="com.vehicle.dao.VehicleDaoImpl" %>
<%@ page import="com.vehicle.model.Vehicle" %>
<%@ page import="java.util.List" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>搜索功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ccc; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
    </style>
</head>
<body>
    <h1>🔍 搜索功能测试页面</h1>
    
    <%
        VehicleDAO dao = new VehicleDaoImpl();
        
        // 测试1: 搜索"丰田"
        try {
            List<Vehicle> result1 = dao.searchVehicles("丰田");
    %>
            <div class="test-result success">
                <h3>✅ 测试1: 搜索"丰田"</h3>
                <p>找到 <%= result1.size() %> 条结果:</p>
                <% for (Vehicle v : result1) { %>
                    <p>- <%= v.getLicensePlate() %> | <%= v.getBrand() %> | <%= v.getModel() %></p>
                <% } %>
            </div>
    <%
        } catch (Exception e) {
    %>
            <div class="test-result error">
                <h3>❌ 测试1失败: <%= e.getMessage() %></h3>
            </div>
    <%
        }
        
        // 测试2: 搜索"京A"
        try {
            List<Vehicle> result2 = dao.searchVehicles("京A");
    %>
            <div class="test-result success">
                <h3>✅ 测试2: 搜索"京A"</h3>
                <p>找到 <%= result2.size() %> 条结果:</p>
                <% for (Vehicle v : result2) { %>
                    <p>- <%= v.getLicensePlate() %> | <%= v.getBrand() %> | <%= v.getModel() %></p>
                <% } %>
            </div>
    <%
        } catch (Exception e) {
    %>
            <div class="test-result error">
                <h3>❌ 测试2失败: <%= e.getMessage() %></h3>
            </div>
    <%
        }
        
        // 测试3: 搜索"维修"
        try {
            List<Vehicle> result3 = dao.searchVehicles("维修");
    %>
            <div class="test-result success">
                <h3>✅ 测试3: 搜索"维修"</h3>
                <p>找到 <%= result3.size() %> 条结果:</p>
                <% for (Vehicle v : result3) { %>
                    <p>- <%= v.getLicensePlate() %> | <%= v.getBrand() %> | <%= v.getModel() %> | <%= v.getStatus() %></p>
                <% } %>
            </div>
    <%
        } catch (Exception e) {
    %>
            <div class="test-result error">
                <h3>❌ 测试3失败: <%= e.getMessage() %></h3>
            </div>
    <%
        }
        
        // 测试4: 搜索空字符串
        try {
            List<Vehicle> result4 = dao.searchVehicles("");
    %>
            <div class="test-result success">
                <h3>✅ 测试4: 搜索空字符串（应返回所有车辆）</h3>
                <p>找到 <%= result4.size() %> 条结果</p>
            </div>
    <%
        } catch (Exception e) {
    %>
            <div class="test-result error">
                <h3>❌ 测试4失败: <%= e.getMessage() %></h3>
            </div>
    <%
        }
        
        // 测试5: 搜索不存在的内容
        try {
            List<Vehicle> result5 = dao.searchVehicles("不存在的车辆");
    %>
            <div class="test-result success">
                <h3>✅ 测试5: 搜索不存在的内容</h3>
                <p>找到 <%= result5.size() %> 条结果（应该为0）</p>
            </div>
    <%
        } catch (Exception e) {
    %>
            <div class="test-result error">
                <h3>❌ 测试5失败: <%= e.getMessage() %></h3>
            </div>
    <%
        }
    %>
    
    <hr>
    <p><a href="vehicle">返回车辆列表</a> | <a href="index.jsp">返回首页</a></p>
</body>
</html>
