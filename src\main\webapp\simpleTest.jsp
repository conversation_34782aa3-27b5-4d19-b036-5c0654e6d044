<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.sql.*" %>
<%@ page import="com.vehicle.util.DBUtil" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>简单数据库连接测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 数据库连接测试</h1>
        
        <%
            Connection conn = null;
            Statement stmt = null;
            ResultSet rs = null;
            
            try {
                // 测试数据库连接
                conn = DBUtil.getConnection();
                
                if (conn != null && !conn.isClosed()) {
                    out.println("<div class='success'>");
                    out.println("<h3>✅ 数据库连接成功！</h3>");
                    out.println("<p>连接信息：" + conn.getMetaData().getURL() + "</p>");
                    out.println("<p>数据库产品：" + conn.getMetaData().getDatabaseProductName() + "</p>");
                    out.println("<p>数据库版本：" + conn.getMetaData().getDatabaseProductVersion() + "</p>");
                    out.println("</div>");
                    
                    // 测试查询车辆表
                    stmt = conn.createStatement();
                    
                    // 先检查表是否存在
                    DatabaseMetaData dbmd = conn.getMetaData();
                    ResultSet tables = dbmd.getTables(null, null, "vehicle", null);
                    
                    if (tables.next()) {
                        out.println("<div class='success'>");
                        out.println("<h3>✅ 车辆表存在</h3>");
                        out.println("</div>");
                        
                        // 查询表结构
                        out.println("<div class='info'>");
                        out.println("<h3>📋 表结构信息</h3>");
                        out.println("<table>");
                        out.println("<tr><th>列名</th><th>数据类型</th><th>允许空值</th></tr>");
                        
                        ResultSet columns = dbmd.getColumns(null, null, "vehicle", null);
                        while (columns.next()) {
                            String columnName = columns.getString("COLUMN_NAME");
                            String dataType = columns.getString("TYPE_NAME");
                            String nullable = columns.getString("IS_NULLABLE");
                            out.println("<tr>");
                            out.println("<td>" + columnName + "</td>");
                            out.println("<td>" + dataType + "</td>");
                            out.println("<td>" + nullable + "</td>");
                            out.println("</tr>");
                        }
                        columns.close();
                        out.println("</table>");
                        out.println("</div>");
                        
                        // 查询数据
                        rs = stmt.executeQuery("SELECT COUNT(*) as total FROM vehicle");
                        if (rs.next()) {
                            int total = rs.getInt("total");
                            out.println("<div class='success'>");
                            out.println("<h3>📊 数据统计</h3>");
                            out.println("<p>总车辆数：" + total + "</p>");
                            out.println("</div>");
                            
                            if (total > 0) {
                                // 显示前5条记录
                                rs.close();
                                rs = stmt.executeQuery("SELECT TOP 5 id, license_plate, brand, model, status FROM vehicle ORDER BY id");
                                
                                out.println("<div class='info'>");
                                out.println("<h3>🚗 车辆数据示例（前5条）</h3>");
                                out.println("<table>");
                                out.println("<tr><th>ID</th><th>车牌号</th><th>品牌</th><th>型号</th><th>状态</th></tr>");
                                
                                while (rs.next()) {
                                    out.println("<tr>");
                                    out.println("<td>" + rs.getInt("id") + "</td>");
                                    out.println("<td>" + rs.getString("license_plate") + "</td>");
                                    out.println("<td>" + rs.getString("brand") + "</td>");
                                    out.println("<td>" + rs.getString("model") + "</td>");
                                    out.println("<td>" + rs.getString("status") + "</td>");
                                    out.println("</tr>");
                                }
                                out.println("</table>");
                                out.println("</div>");
                            }
                        }
                    } else {
                        out.println("<div class='error'>");
                        out.println("<h3>❌ 车辆表不存在</h3>");
                        out.println("<p>请先执行数据库初始化脚本创建表。</p>");
                        out.println("</div>");
                    }
                    tables.close();
                    
                } else {
                    out.println("<div class='error'>");
                    out.println("<h3>❌ 数据库连接失败</h3>");
                    out.println("<p>连接对象为空或已关闭</p>");
                    out.println("</div>");
                }
                
            } catch (SQLException e) {
                out.println("<div class='error'>");
                out.println("<h3>❌ 数据库错误</h3>");
                out.println("<p><strong>错误类型：</strong>" + e.getClass().getSimpleName() + "</p>");
                out.println("<p><strong>错误信息：</strong>" + e.getMessage() + "</p>");
                out.println("<p><strong>SQL状态：</strong>" + e.getSQLState() + "</p>");
                out.println("<p><strong>错误代码：</strong>" + e.getErrorCode() + "</p>");
                out.println("</div>");
                
                out.println("<div class='info'>");
                out.println("<h3>💡 可能的解决方案：</h3>");
                out.println("<ul>");
                out.println("<li>检查SQL Server服务是否正在运行</li>");
                out.println("<li>确认数据库名称 'vehicle_db' 是否正确</li>");
                out.println("<li>检查Windows用户是否有数据库访问权限</li>");
                out.println("<li>尝试使用SQL Server身份验证</li>");
                out.println("<li>检查SQL Server是否启用了TCP/IP协议</li>");
                out.println("</ul>");
                out.println("</div>");
                
            } catch (Exception e) {
                out.println("<div class='error'>");
                out.println("<h3>❌ 系统错误</h3>");
                out.println("<p><strong>错误类型：</strong>" + e.getClass().getSimpleName() + "</p>");
                out.println("<p><strong>错误信息：</strong>" + e.getMessage() + "</p>");
                out.println("</div>");
            } finally {
                // 关闭资源
                if (rs != null) try { rs.close(); } catch (SQLException e) {}
                if (stmt != null) try { stmt.close(); } catch (SQLException e) {}
                if (conn != null) try { conn.close(); } catch (SQLException e) {}
            }
        %>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="index.jsp" style="padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;">返回首页</a>
        </div>
    </div>
</body>
</html>
