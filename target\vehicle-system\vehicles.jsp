<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.sql.*" %>
<%@ page import="com.vehicle.util.DBUtil" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>车辆管理系统 - 车辆列表</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .toolbar {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }
        
        .btn-sm {
            padding: 8px 16px;
            font-size: 12px;
            margin: 0 2px;
        }
        
        .search-box {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .search-box input {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            width: 250px;
        }
        
        .search-box input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .content {
            padding: 30px;
        }
        
        .vehicle-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .vehicle-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
        }
        
        .vehicle-table td {
            padding: 15px;
            border-bottom: 1px solid #f1f3f4;
            font-size: 14px;
        }
        
        .vehicle-table tr:hover {
            background: #f8f9fa;
        }
        
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            min-width: 80px;
            display: inline-block;
        }
        
        .status-available {
            background: #d4edda;
            color: #155724;
        }
        
        .status-rented {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-maintenance {
            background: #f8d7da;
            color: #721c24;
        }
        
        .actions {
            display: flex;
            gap: 5px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 车辆管理系统</h1>
            <p>专业的车辆信息管理平台</p>
        </div>
        
        <div class="toolbar">
            <div>
                <a href="addVehicle.jsp" class="btn btn-primary">
                    ➕ 添加新车辆
                </a>
                <a href="index.jsp" class="btn btn-success">
                    🏠 返回首页
                </a>
            </div>
            <div class="search-box">
                <input type="text" id="searchInput" placeholder="搜索车牌号、品牌或型号..." onkeyup="searchVehicles()">
                <button class="btn btn-primary" onclick="searchVehicles()">🔍 搜索</button>
            </div>
        </div>
        
        <div class="content">
            <%
                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;
                
                int totalVehicles = 0;
                int availableVehicles = 0;
                int rentedVehicles = 0;
                int maintenanceVehicles = 0;
                
                try {
                    conn = DBUtil.getConnection();
                    stmt = conn.createStatement();
                    
                    // 统计数据
                    rs = stmt.executeQuery("SELECT status, COUNT(*) as count FROM vehicle GROUP BY status");
                    while (rs.next()) {
                        String status = rs.getString("status");
                        int count = rs.getInt("count");
                        totalVehicles += count;
                        
                        if ("available".equals(status)) {
                            availableVehicles = count;
                        } else if ("rented".equals(status)) {
                            rentedVehicles = count;
                        } else if ("maintenance".equals(status)) {
                            maintenanceVehicles = count;
                        }
                    }
            %>
            
            <!-- 统计卡片 -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number"><%= totalVehicles %></div>
                    <div class="stat-label">总车辆数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><%= availableVehicles %></div>
                    <div class="stat-label">可用车辆</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><%= rentedVehicles %></div>
                    <div class="stat-label">已租出</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><%= maintenanceVehicles %></div>
                    <div class="stat-label">维修中</div>
                </div>
            </div>

            <%
                    // 获取车辆列表
                    rs = stmt.executeQuery("SELECT * FROM vehicle ORDER BY id DESC");

                    if (!rs.isBeforeFirst()) {
            %>
                        <div class="empty-state">
                            <div style="font-size: 4em; margin-bottom: 20px;">🚗</div>
                            <h3>暂无车辆数据</h3>
                            <p>点击上方"添加新车辆"按钮开始添加车辆信息</p>
                        </div>
            <%
                    } else {
            %>
                        <table class="vehicle-table" id="vehicleTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>车牌号</th>
                                    <th>品牌</th>
                                    <th>型号</th>
                                    <th>购买日期</th>
                                    <th>里程数</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
            <%
                        while (rs.next()) {
                            int id = rs.getInt("id");
                            String licensePlate = rs.getString("license_plate");
                            String brand = rs.getString("brand");
                            String model = rs.getString("model");
                            Date purchaseDate = rs.getDate("purchase_date");
                            int mileage = rs.getInt("mileage");
                            String status = rs.getString("status");

                            String statusClass = "";
                            String statusText = "";
                            if ("available".equals(status)) {
                                statusClass = "status-available";
                                statusText = "可用";
                            } else if ("rented".equals(status)) {
                                statusClass = "status-rented";
                                statusText = "已租出";
                            } else if ("maintenance".equals(status)) {
                                statusClass = "status-maintenance";
                                statusText = "维修中";
                            } else {
                                statusClass = "status-available";
                                statusText = status;
                            }
            %>
                                <tr>
                                    <td><%= id %></td>
                                    <td><strong><%= licensePlate %></strong></td>
                                    <td><%= brand %></td>
                                    <td><%= model %></td>
                                    <td><%= purchaseDate != null ? purchaseDate.toString() : "未知" %></td>
                                    <td><%= String.format("%,d", mileage) %> 公里</td>
                                    <td><span class="status <%= statusClass %>"><%= statusText %></span></td>
                                    <td>
                                        <div class="actions">
                                            <a href="editVehicle.jsp?id=<%= id %>" class="btn btn-warning btn-sm">✏️ 编辑</a>
                                            <a href="deleteVehicle.jsp?id=<%= id %>" class="btn btn-danger btn-sm"
                                               onclick="return confirm('确定要删除车辆 <%= licensePlate %> 吗？')">🗑️ 删除</a>
                                        </div>
                                    </td>
                                </tr>
            <%
                        }
            %>
                            </tbody>
                        </table>
            <%
                    }
                } catch (SQLException e) {
                    out.println("<div style='color: red; padding: 20px; background: #f8d7da; border-radius: 8px;'>");
                    out.println("<h3>❌ 数据库连接错误</h3>");
                    out.println("<p>错误信息: " + e.getMessage() + "</p>");
                    out.println("<p>请检查数据库连接配置和服务状态。</p>");
                    out.println("</div>");
                } finally {
                    if (rs != null) try { rs.close(); } catch (SQLException e) {}
                    if (stmt != null) try { stmt.close(); } catch (SQLException e) {}
                    if (conn != null) try { conn.close(); } catch (SQLException e) {}
                }
            %>
        </div>
    </div>

    <script>
        function searchVehicles() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toUpperCase();
            const table = document.getElementById('vehicleTable');

            if (!table) return;

            const tr = table.getElementsByTagName('tr');

            for (let i = 1; i < tr.length; i++) {
                const td = tr[i].getElementsByTagName('td');
                let txtValue = '';

                // 搜索车牌号、品牌、型号
                for (let j = 1; j <= 3; j++) {
                    if (td[j]) {
                        txtValue += td[j].textContent || td[j].innerText;
                    }
                }

                if (txtValue.toUpperCase().indexOf(filter) > -1) {
                    tr[i].style.display = '';
                } else {
                    tr[i].style.display = 'none';
                }
            }
        }

        // 页面加载完成后的动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
