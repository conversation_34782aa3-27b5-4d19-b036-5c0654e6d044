@echo off
chcp 65001 >nul
title Fix PATH Environment Variable

echo ========================================
echo PATH Environment Variable Fix Tool
echo ========================================
echo.
echo This script will remove corrupted PATH entries:
echo - E:\Jave*IntelliJ IDEA*
echo - G:\Vscode*Microsoft VS Code*
echo.
echo Note: Administrator privileges required
echo ========================================
echo.

REM Check administrator privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo Administrator privileges detected, continuing...
    echo.
) else (
    echo ERROR: Administrator privileges required!
    echo Please right-click this file and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo Backing up current PATH...
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set "ORIGINAL_PATH=%%b"
if defined ORIGINAL_PATH (
    echo %ORIGINAL_PATH% > path_backup.txt
    echo PATH backed up to path_backup.txt
) else (
    echo Warning: Could not backup PATH
)

echo.
echo Cleaning corrupted PATH entries...

REM Use PowerShell to clean PATH
powershell -ExecutionPolicy Bypass -Command "try { $currentPath = [Environment]::GetEnvironmentVariable('PATH', 'Machine'); if ($currentPath) { $cleanPath = ($currentPath -split ';' | Where-Object { $_ -notlike '*Jave*IntelliJ*' -and $_ -notlike '*Vscode*Microsoft VS Code*' -and $_ -ne '' }) -join ';'; [Environment]::SetEnvironmentVariable('PATH', $cleanPath, 'Machine'); Write-Host 'PATH cleaned successfully' } else { Write-Host 'Could not read PATH' } } catch { Write-Host 'Error cleaning PATH:' $_.Exception.Message }"

echo.
echo ========================================
echo Fix completed!
echo ========================================
echo.
echo Corrupted PATH entries have been removed
echo Original PATH backed up to path_backup.txt
echo.
echo Please restart your computer for changes to take effect
echo After restart, conda environment issues should be resolved
echo.

pause
