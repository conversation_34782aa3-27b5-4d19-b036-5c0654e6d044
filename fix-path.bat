@echo off
title 修复PATH环境变量

echo ========================================
echo PATH环境变量修复工具
echo ========================================
echo.
echo 此脚本将删除PATH中包含乱码的条目：
echo - E:\Jave*IntelliJ IDEA*
echo - G:\Vscode*Microsoft VS Code*
echo.
echo 注意：此操作需要管理员权限
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限，继续执行...
    echo.
) else (
    echo 错误：需要管理员权限！
    echo 请右键点击此文件，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo 正在备份当前PATH...
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH') do set "ORIGINAL_PATH=%%b"
echo %ORIGINAL_PATH% > path_backup.txt
echo PATH已备份到 path_backup.txt

echo.
echo 正在清理PATH中的损坏条目...

REM 使用PowerShell清理PATH
powershell -Command "& {$currentPath = [Environment]::GetEnvironmentVariable('PATH', 'Machine'); $cleanPath = ($currentPath -split ';' | Where-Object { $_ -notlike '*Jave*IntelliJ*' -and $_ -notlike '*Vscode*Microsoft VS Code*' -and $_ -ne '' }) -join ';'; [Environment]::SetEnvironmentVariable('PATH', $cleanPath, 'Machine'); Write-Host 'PATH已清理完成'}"

if %errorLevel% == 0 (
    echo.
    echo ========================================
    echo 修复完成！
    echo ========================================
    echo.
    echo 已删除包含乱码的PATH条目
    echo 原始PATH已备份到 path_backup.txt
    echo.
    echo 请重新启动计算机以使更改生效
    echo 重启后conda环境问题应该得到解决
    echo.
) else (
    echo.
    echo 修复失败！请手动删除PATH中的损坏条目
    echo.
)

pause
