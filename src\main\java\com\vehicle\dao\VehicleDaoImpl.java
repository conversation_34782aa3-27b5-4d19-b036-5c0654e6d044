package com.vehicle.dao;

import com.vehicle.model.Vehicle;
import com.vehicle.util.DBUtil;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class VehicleDaoImpl implements VehicleDAO {
    
    @Override
    public List<Vehicle> getAllVehicles() throws SQLException {
        List<Vehicle> vehicles = new ArrayList<>();
        String sql = "SELECT * FROM vehicle";
        
        try (Connection conn = DBUtil.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {
            
            while (rs.next()) {
                Vehicle vehicle = new Vehicle();
                vehicle.setId(rs.getInt("id"));
                vehicle.setLicensePlate(rs.getString("license_plate"));
                vehicle.setBrand(rs.getString("brand"));
                vehicle.setModel(rs.getString("model"));
                vehicle.setPurchaseDate(rs.getDate("purchase_date"));
                vehicle.setMileage(rs.getInt("mileage"));
                vehicle.setStatus(rs.getString("status"));
                vehicles.add(vehicle);
            }
        }
        return vehicles;
    }
    
    // 添加更多DAO方法（增删改查）...
}