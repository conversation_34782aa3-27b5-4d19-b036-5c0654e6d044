package com.vehicle.dao;

import com.vehicle.model.Vehicle;
import com.vehicle.util.DBUtil;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;

public class VehicleDaoImpl implements VehicleDAO {
    
    @Override
    public List<Vehicle> getAllVehicles() throws SQLException {
        List<Vehicle> vehicles = new ArrayList<>();
        String sql = "SELECT * FROM vehicle ORDER BY id";

        try (Connection conn = DBUtil.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                Vehicle vehicle = createVehicleFromResultSet(rs);
                vehicles.add(vehicle);
            }
        }
        return vehicles;
    }

    @Override
    public List<Vehicle> searchVehicles(String keyword) throws SQLException {
        List<Vehicle> vehicles = new ArrayList<>();

        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllVehicles(); // 如果搜索关键词为空，返回所有车辆
        }

        String sql = "SELECT * FROM vehicle WHERE " +
                    "license_plate LIKE ? OR " +
                    "brand LIKE ? OR " +
                    "model LIKE ? OR " +
                    "status LIKE ? " +
                    "ORDER BY id";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            String searchPattern = "%" + keyword.trim() + "%";
            pstmt.setString(1, searchPattern);
            pstmt.setString(2, searchPattern);
            pstmt.setString(3, searchPattern);
            pstmt.setString(4, searchPattern);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    Vehicle vehicle = createVehicleFromResultSet(rs);
                    vehicles.add(vehicle);
                }
            }
        }
        return vehicles;
    }

    @Override
    public List<Vehicle> getVehiclesByStatus(String status) throws SQLException {
        List<Vehicle> vehicles = new ArrayList<>();

        if (status == null || status.trim().isEmpty()) {
            return getAllVehicles(); // 如果状态为空，返回所有车辆
        }

        String sql = "SELECT * FROM vehicle WHERE status = ? ORDER BY id";

        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, status.trim());

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    Vehicle vehicle = createVehicleFromResultSet(rs);
                    vehicles.add(vehicle);
                }
            }
        }
        return vehicles;
    }

    /**
     * 从ResultSet创建Vehicle对象的辅助方法
     */
    private Vehicle createVehicleFromResultSet(ResultSet rs) throws SQLException {
        Vehicle vehicle = new Vehicle();
        vehicle.setId(rs.getInt("id"));
        vehicle.setLicensePlate(rs.getString("license_plate"));
        vehicle.setBrand(rs.getString("brand"));
        vehicle.setModel(rs.getString("model"));

        // 处理日期类型转换：java.sql.Date -> java.util.Date
        java.sql.Date sqlDate = rs.getDate("purchase_date");
        if (sqlDate != null) {
            vehicle.setPurchaseDate(new java.util.Date(sqlDate.getTime()));
        }

        vehicle.setMileage(rs.getInt("mileage"));
        vehicle.setStatus(rs.getString("status"));
        return vehicle;
    }
}