@echo off
title Vehicle Management System

echo ========================================
echo Vehicle Management System
echo ========================================
echo.
echo Starting project...
echo URL: http://localhost:8080/vehicle
echo Press Ctrl+C to stop
echo ========================================
echo.

cd /d "D:\projects\vehicle-system"
set "JAVA_HOME=D:\javaJDK8"
"D:\XunLai\apache-maven-3.9.10-bin\apache-maven-3.9.10\bin\mvn.cmd" clean compile tomcat7:run

echo.
echo Project stopped.
pause
