@echo off
setlocal EnableDelayedExpansion

REM 完全重置环境变量，避免conda干扰
set "PATH="
set "PYTHONPATH="
set "CONDA_DEFAULT_ENV="
set "CONDA_EXE="
set "CONDA_PREFIX="
set "CONDA_PROMPT_MODIFIER="
set "CONDA_PYTHON_EXE="
set "CONDA_ROOT="
set "CONDA_SHLVL="

REM 设置最小化的系统PATH
set "PATH=C:\Windows\System32;C:\Windows;C:\Windows\System32\Wbem"

REM 设置Java环境
set "JAVA_HOME=D:\javaJDK8"
set "PATH=%JAVA_HOME%\bin;%PATH%"

REM 设置Maven环境
set "MAVEN_HOME=D:\XunLai\apache-maven-3.9.10-bin\apache-maven-3.9.10"
set "PATH=%MAVEN_HOME%\bin;%PATH%"

title Vehicle Management System

echo ========================================
echo Vehicle Management System Launcher
echo ========================================
echo.

REM 验证Java
echo Checking Java...
java -version 2>nul
if errorlevel 1 (
    echo ERROR: Java not working properly
    echo JAVA_HOME: %JAVA_HOME%
    pause
    exit /b 1
)
echo Java OK
echo.

REM 验证Maven
echo Checking Maven...
mvn -version 2>nul
if errorlevel 1 (
    echo ERROR: Maven not working properly
    echo MAVEN_HOME: %MAVEN_HOME%
    pause
    exit /b 1
)
echo Maven OK
echo.

REM 切换到项目目录
echo Changing to project directory...
cd /d "D:\projects\vehicle-system"
if not exist "pom.xml" (
    echo ERROR: pom.xml not found in current directory
    echo Current directory: %CD%
    pause
    exit /b 1
)
echo Project directory OK
echo.

echo ========================================
echo Starting Vehicle Management System...
echo ========================================
echo Project URL: http://localhost:8080/vehicle
echo Press Ctrl+C to stop the server
echo ========================================
echo.

REM 启动Maven项目
mvn clean compile tomcat7:run

echo.
echo Project stopped.
pause
