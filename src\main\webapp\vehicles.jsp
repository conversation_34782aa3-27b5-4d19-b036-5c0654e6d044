<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ page import="java.sql.*" %>
<%@ page import="java.util.Date" %>
<%@ page import="com.vehicle.util.DBUtil" %>
<%@ page import="com.vehicle.model.Vehicle" %>
<%@ page import="java.util.List" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>车辆管理系统 - 车辆列表</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .toolbar {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            color: white;
        }
        
        .btn-sm {
            padding: 8px 16px;
            font-size: 12px;
            margin: 0 2px;
        }
        
        .search-box {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .search-box input {
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            width: 250px;
        }
        
        .search-box input:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .content {
            padding: 30px;
        }
        
        .vehicle-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .vehicle-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            font-size: 14px;
        }
        
        .vehicle-table td {
            padding: 15px;
            border-bottom: 1px solid #f1f3f4;
            font-size: 14px;
        }
        
        .vehicle-table tr:hover {
            background: #f8f9fa;
        }
        
        .status {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            min-width: 80px;
            display: inline-block;
        }
        
        .status-available {
            background: #d4edda;
            color: #155724;
        }
        
        .status-rented {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-maintenance {
            background: #f8d7da;
            color: #721c24;
        }
        
        .actions {
            display: flex;
            gap: 5px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 车辆管理系统</h1>
            <p>专业的车辆信息管理平台</p>
        </div>
        
        <div class="toolbar">
            <div>
                <a href="addVehicle.jsp" class="btn btn-primary">
                    ➕ 添加新车辆
                </a>
                <a href="index.jsp" class="btn btn-success">
                    🏠 返回首页
                </a>
            </div>
            <div class="search-box">
                <form method="post" action="vehicle" style="display: flex; align-items: center; gap: 10px;">
                    <input type="text" name="search" placeholder="搜索车牌号、品牌或型号..."
                           value="${searchKeyword}" style="padding: 10px 15px; border: 2px solid #e9ecef; border-radius: 8px; font-size: 14px; width: 250px;">
                    <button type="submit" class="btn btn-primary">🔍 搜索</button>
                    <a href="vehicle" class="btn btn-success">🔄 显示全部</a>
                </form>
            </div>
        </div>
        
        <div class="content">
            <!-- 显示搜索结果信息 -->
            <c:if test="${not empty message}">
                <div style="background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #17a2b8;">
                    <strong>📊 ${message}</strong>
                </div>
            </c:if>

            <!-- 显示错误信息 -->
            <c:if test="${not empty error}">
                <div style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #dc3545;">
                    <strong>❌ ${error}</strong>
                </div>
            </c:if>

            <%
                // 首先尝试从Servlet获取数据
                List<Vehicle> vehicles = (List<Vehicle>) request.getAttribute("vehicles");

                Connection conn = null;
                Statement stmt = null;
                ResultSet rs = null;

                int totalVehicles = 0;
                int availableVehicles = 0;
                int rentedVehicles = 0;
                int maintenanceVehicles = 0;

                try {
                    // 如果Servlet没有提供数据，则直接查询数据库
                    if (vehicles == null) {
                        conn = DBUtil.getConnection();
                        stmt = conn.createStatement();

                        // 统计数据
                        rs = stmt.executeQuery("SELECT status, COUNT(*) as count FROM vehicle GROUP BY status");
                        while (rs.next()) {
                            String status = rs.getString("status");
                            int count = rs.getInt("count");
                            totalVehicles += count;

                            if ("可用".equals(status)) {
                                availableVehicles = count;
                            } else if ("已租出".equals(status)) {
                                rentedVehicles = count;
                            } else if ("维修中".equals(status)) {
                                maintenanceVehicles = count;
                            }
                        }
                    } else {
                        // 使用Servlet提供的数据进行统计
                        totalVehicles = vehicles.size();
                        for (Vehicle v : vehicles) {
                            String status = v.getStatus();
                            if ("可用".equals(status)) {
                                availableVehicles++;
                            } else if ("已租出".equals(status)) {
                                rentedVehicles++;
                            } else if ("维修中".equals(status)) {
                                maintenanceVehicles++;
                            }
                        }
                    }
            %>
            
            <!-- 统计卡片 -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number"><%= totalVehicles %></div>
                    <div class="stat-label">总车辆数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><%= availableVehicles %></div>
                    <div class="stat-label">可用车辆</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><%= rentedVehicles %></div>
                    <div class="stat-label">已租出</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><%= maintenanceVehicles %></div>
                    <div class="stat-label">维修中</div>
                </div>
            </div>

            <%
                    // 显示车辆列表
                    boolean hasVehicles = false;
                    boolean isSearchResult = false;

                    if (vehicles != null) {
                        // Servlet提供了数据（可能是搜索结果或所有车辆）
                        hasVehicles = !vehicles.isEmpty();
                        isSearchResult = request.getParameter("search") != null;
                    } else {
                        // 如果Servlet没有提供数据，直接查询数据库
                        if (rs != null) rs.close();
                        rs = stmt.executeQuery("SELECT * FROM vehicle ORDER BY id DESC");
                        hasVehicles = rs.isBeforeFirst();
                    }

                    if (!hasVehicles) {
            %>
                        <div class="empty-state">
                            <div style="font-size: 4em; margin-bottom: 20px;">🚗</div>
                            <% if (isSearchResult) { %>
                                <h3>暂无车辆数据</h3>
                                <p>搜索关键词 "<strong><%= request.getParameter("search") %></strong>" 没有找到匹配的车辆</p>
                                <p style="color: #6c757d;">请尝试其他关键词或检查拼写</p>
                                <a href="vehicle" class="btn btn-primary" style="margin-top: 15px;">显示所有车辆</a>
                            <% } else { %>
                                <h3>暂无车辆数据</h3>
                                <p>点击上方"添加新车辆"按钮开始添加车辆信息</p>
                            <% } %>
                        </div>
            <%
                    } else {
            %>
                        <table class="vehicle-table" id="vehicleTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>车牌号</th>
                                    <th>品牌</th>
                                    <th>型号</th>
                                    <th>购买日期</th>
                                    <th>里程数</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
            <%
                        if (vehicles != null) {
                            // 使用Servlet提供的数据
                            for (Vehicle vehicle : vehicles) {
                                int id = vehicle.getId();
                                String licensePlate = vehicle.getLicensePlate();
                                String brand = vehicle.getBrand();
                                String model = vehicle.getModel();
                                java.util.Date purchaseDate = vehicle.getPurchaseDate();
                                int mileage = vehicle.getMileage();
                                String status = vehicle.getStatus();

                                String statusClass = "";
                                String statusText = status;
                                if ("可用".equals(status)) {
                                    statusClass = "status-available";
                                } else if ("已租出".equals(status)) {
                                    statusClass = "status-rented";
                                } else if ("维修中".equals(status)) {
                                    statusClass = "status-maintenance";
                                } else {
                                    statusClass = "status-available";
                                }
            %>
                                <tr>
                                    <td><%= id %></td>
                                    <td><strong><%= licensePlate %></strong></td>
                                    <td><%= brand %></td>
                                    <td><%= model %></td>
                                    <td><%= purchaseDate != null ? purchaseDate.toString() : "未知" %></td>
                                    <td><%= String.format("%,d", mileage) %> 公里</td>
                                    <td><span class="status <%= statusClass %>"><%= statusText %></span></td>
                                    <td>
                                        <div class="actions">
                                            <a href="editVehicle.jsp?id=<%= id %>" class="btn btn-warning btn-sm">✏️ 编辑</a>
                                            <a href="deleteVehicle.jsp?id=<%= id %>" class="btn btn-danger btn-sm"
                                               onclick="return confirm('确定要删除车辆 <%= licensePlate %> 吗？')">🗑️ 删除</a>
                                        </div>
                                    </td>
                                </tr>
            <%
                            }
                        } else {
                            // 使用数据库查询结果
                            while (rs.next()) {
                                int id = rs.getInt("id");
                                String licensePlate = rs.getString("license_plate");
                                String brand = rs.getString("brand");
                                String model = rs.getString("model");
                                Date purchaseDate = rs.getDate("purchase_date");
                                int mileage = rs.getInt("mileage");
                                String status = rs.getString("status");

                                String statusClass = "";
                                String statusText = status;
                                if ("可用".equals(status)) {
                                    statusClass = "status-available";
                                } else if ("已租出".equals(status)) {
                                    statusClass = "status-rented";
                                } else if ("维修中".equals(status)) {
                                    statusClass = "status-maintenance";
                                } else {
                                    statusClass = "status-available";
                                }
            %>
                                <tr>
                                    <td><%= id %></td>
                                    <td><strong><%= licensePlate %></strong></td>
                                    <td><%= brand %></td>
                                    <td><%= model %></td>
                                    <td><%= purchaseDate != null ? purchaseDate.toString() : "未知" %></td>
                                    <td><%= String.format("%,d", mileage) %> 公里</td>
                                    <td><span class="status <%= statusClass %>"><%= statusText %></span></td>
                                    <td>
                                        <div class="actions">
                                            <a href="editVehicle.jsp?id=<%= id %>" class="btn btn-warning btn-sm">✏️ 编辑</a>
                                            <a href="deleteVehicle.jsp?id=<%= id %>" class="btn btn-danger btn-sm"
                                               onclick="return confirm('确定要删除车辆 <%= licensePlate %> 吗？')">🗑️ 删除</a>
                                        </div>
                                    </td>
                                </tr>
            <%
                            }
                        }
            %>
                            </tbody>
                        </table>
            <%
                    }
                } catch (SQLException e) {
                    out.println("<div style='color: red; padding: 20px; background: #f8d7da; border-radius: 8px;'>");
                    out.println("<h3>❌ 数据库连接错误</h3>");
                    out.println("<p>错误信息: " + e.getMessage() + "</p>");
                    out.println("<p>请检查数据库连接配置和服务状态。</p>");
                    out.println("</div>");
                } finally {
                    if (rs != null) try { rs.close(); } catch (SQLException e) {}
                    if (stmt != null) try { stmt.close(); } catch (SQLException e) {}
                    if (conn != null) try { conn.close(); } catch (SQLException e) {}
                }
            %>
        </div>
    </div>

    <script>
        // 页面加载完成后的动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.stat-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';

                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });

        // 搜索表单回车提交
        document.querySelector('input[name="search"]').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                this.form.submit();
            }
        });
    </script>
</body>
</html>
