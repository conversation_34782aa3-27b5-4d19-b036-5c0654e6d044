-- =====================================================
-- 车辆管理系统 - 数据库初始化脚本
-- 课程设计项目
-- 创建时间: 2025-06-17
-- =====================================================

-- 使用master数据库
USE master;
GO

-- 删除现有数据库（如果存在）
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'vehicle_db')
BEGIN
    ALTER DATABASE vehicle_db SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE vehicle_db;
    PRINT '已删除现有数据库 vehicle_db';
END
GO

-- 删除现有登录用户（如果存在）
IF EXISTS (SELECT name FROM sys.server_principals WHERE name = 'vehicle_user')
BEGIN
    DROP LOGIN vehicle_user;
    PRINT '已删除现有登录用户 vehicle_user';
END
GO

-- 创建新的登录用户
CREATE LOGIN vehicle_user WITH PASSWORD = 'Vehicle123!';
PRINT '已创建登录用户 vehicle_user';
GO

-- 创建数据库
CREATE DATABASE vehicle_db
ON 
( NAME = 'vehicle_db',
  FILENAME = 'C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\vehicle_db.mdf',
  SIZE = 100MB,
  MAXSIZE = 1GB,
  FILEGROWTH = 10MB )
LOG ON 
( NAME = 'vehicle_db_log',
  FILENAME = 'C:\Program Files\Microsoft SQL Server\MSSQL16.MSSQLSERVER\MSSQL\DATA\vehicle_db_log.ldf',
  SIZE = 10MB,
  MAXSIZE = 100MB,
  FILEGROWTH = 5MB );
PRINT '已创建数据库 vehicle_db';
GO

-- 切换到新创建的数据库
USE vehicle_db;
GO

-- 创建数据库用户并分配权限
CREATE USER vehicle_user FOR LOGIN vehicle_user;
ALTER ROLE db_owner ADD MEMBER vehicle_user;
PRINT '已创建数据库用户并分配权限';
GO

PRINT '=== 数据库和用户创建完成 ===';
