@echo off
echo 🔍 检查SQL Server端口信息
echo ================================

echo.
echo 📋 1. 检查SQL Server服务状态：
sc query MSSQLSERVER
echo.

echo 📋 2. 检查端口1433是否被占用：
netstat -an | findstr :1433
echo.

echo 📋 3. 检查所有SQL Server相关端口：
netstat -an | findstr sqlservr
echo.

echo 📋 4. 检查所有监听的端口：
netstat -an | findstr LISTENING | findstr :14
echo.

echo 📋 5. 尝试连接到localhost:1433：
telnet localhost 1433 2>nul
if %errorlevel% equ 0 (
    echo ✅ 端口1433可以连接
) else (
    echo ❌ 端口1433无法连接
)

echo.
echo 📋 6. 检查防火墙状态：
netsh advfirewall show allprofiles state

echo.
echo 🔧 如果端口1433无法连接，可能的原因：
echo - SQL Server未启动
echo - TCP/IP协议未启用
echo - 使用了动态端口
echo - 防火墙阻止了连接
echo.
pause
