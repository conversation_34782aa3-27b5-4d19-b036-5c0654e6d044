package com.vehicle.model;

import java.util.Date;

public class Vehicle {
    private int id;
    private String licensePlate;
    private String brand;
    private String model;
    private Date purchaseDate;
    private int mileage;
    private String status;
    
    // 无参构造函数
    public Vehicle() {}
    
    // 全参构造函数
    public Vehicle(int id, String licensePlate, String brand, String model, Date purchaseDate, int mileage, String status) {
        this.id = id;
        this.licensePlate = licensePlate;
        this.brand = brand;
        this.model = model;
        this.purchaseDate = purchaseDate;
        this.mileage = mileage;
        this.status = status;
    }
    
    // Getter/Setter方法
    public int getId() { return id; }
    public void setId(int id) { this.id = id; }
    
    public String getLicensePlate() { return licensePlate; }
    public void setLicensePlate(String licensePlate) { this.licensePlate = licensePlate; }
    
    public String getBrand() { return brand; }
    public void setBrand(String brand) { this.brand = brand; }
    
    public String getModel() { return model; }
    public void setModel(String model) { this.model = model; }
    
    public Date getPurchaseDate() { return purchaseDate; }
    public void setPurchaseDate(Date purchaseDate) { this.purchaseDate = purchaseDate; }
    
    public int getMileage() { return mileage; }
    public void setMileage(int mileage) { this.mileage = mileage; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    @Override
    public String toString() {
        return "Vehicle{" +
                "id=" + id +
                ", licensePlate='" + licensePlate + '\'' +
                ", brand='" + brand + '\'' +
                ", model='" + model + '\'' +
                ", purchaseDate=" + purchaseDate +
                ", mileage=" + mileage +
                ", status='" + status + '\'' +
                '}';
    }
}