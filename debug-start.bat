@echo off
title Vehicle Management System Debug

echo ========================================
echo Vehicle Management System Debug Launcher
echo ========================================
echo.

echo Step 1: Setting up environment...
set "PATH=C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem"
set "JAVA_HOME=D:\javaJDK8"
set "MAVEN_HOME=D:\XunLai\apache-maven-3.9.10-bin\apache-maven-3.9.10"
set "PATH=%JAVA_HOME%\bin;%MAVEN_HOME%\bin;%PATH%"

echo Current directory: %CD%
echo JAVA_HOME: %JAVA_HOME%
echo MAVEN_HOME: %MAVEN_HOME%
echo.

echo Step 2: Checking if Java exists...
if exist "%JAVA_HOME%\bin\java.exe" (
    echo Java executable found at: %JAVA_HOME%\bin\java.exe
) else (
    echo ERROR: Java executable NOT found at: %JAVA_HOME%\bin\java.exe
    echo Please check if Java is installed correctly
    pause
    exit /b 1
)

echo Step 3: Testing Java...
"%JAVA_HOME%\bin\java.exe" -version
if errorlevel 1 (
    echo ERROR: Java test failed!
    pause
    exit /b 1
) else (
    echo Java test successful!
)
echo.

echo Step 4: Checking if Maven exists...
if exist "%MAVEN_HOME%\bin\mvn.cmd" (
    echo Maven executable found at: %MAVEN_HOME%\bin\mvn.cmd
) else (
    echo ERROR: Maven executable NOT found at: %MAVEN_HOME%\bin\mvn.cmd
    echo Please check if Maven is installed correctly
    pause
    exit /b 1
)

echo Step 5: Testing Maven...
"%MAVEN_HOME%\bin\mvn.cmd" -version
if errorlevel 1 (
    echo ERROR: Maven test failed!
    pause
    exit /b 1
) else (
    echo Maven test successful!
)
echo.

echo Step 6: Checking project directory...
if exist "D:\projects\vehicle-system" (
    echo Project directory found: D:\projects\vehicle-system
) else (
    echo ERROR: Project directory NOT found: D:\projects\vehicle-system
    pause
    exit /b 1
)

echo Step 7: Changing to project directory...
cd /d "D:\projects\vehicle-system"
echo Current directory after change: %CD%

echo Step 8: Checking for pom.xml...
if exist "pom.xml" (
    echo pom.xml found in current directory
) else (
    echo ERROR: pom.xml NOT found in current directory
    echo Current directory contents:
    dir /b
    pause
    exit /b 1
)

echo.
echo ========================================
echo All checks passed! Starting Maven...
echo ========================================
echo Project will be available at: http://localhost:8080/vehicle
echo Press Ctrl+C to stop the server
echo.

"%MAVEN_HOME%\bin\mvn.cmd" clean compile tomcat7:run

echo.
echo Maven process ended.
echo Press any key to exit...
pause
